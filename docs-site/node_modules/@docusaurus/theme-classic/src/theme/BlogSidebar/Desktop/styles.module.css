/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

.sidebar {
  max-height: calc(100vh - (var(--ifm-navbar-height) + 2rem));
  overflow-y: auto;
  position: sticky;
  top: calc(var(--ifm-navbar-height) + 2rem);
}

.sidebarItemTitle {
  font-size: var(--ifm-h3-font-size);
  font-weight: var(--ifm-font-weight-bold);
}

.sidebarItemList {
  font-size: 0.9rem;
}

.sidebarItem {
  margin-top: 0.7rem;
}

.sidebarItemLink {
  color: var(--ifm-font-color-base);
  display: block;
}

.sidebarItemLink:hover {
  text-decoration: none;
}

.sidebarItemLinkActive {
  color: var(--ifm-color-primary) !important;
}

@media (max-width: 996px) {
  .sidebar {
    display: none;
  }
}

.yearGroupHeading {
  margin-top: 1.6rem;
  margin-bottom: 0.4rem;
}
