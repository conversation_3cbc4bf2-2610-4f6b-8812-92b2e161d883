{"theme.AnnouncementBar.closeButtonAriaLabel": "<PERSON><PERSON><PERSON>", "theme.BackToTopButton.buttonAriaLabel": "<PERSON><PERSON><PERSON> kembali ke atas", "theme.CodeBlock.copied": "Di<PERSON>in", "theme.CodeBlock.copy": "<PERSON><PERSON>", "theme.CodeBlock.copyButtonAriaLabel": "<PERSON>in kode ke papan klip", "theme.CodeBlock.wordWrapToggle": "<PERSON>h<PERSON> pembungkus kata", "theme.DocSidebarItem.collapseCategoryAriaLabel": "Ciutkan kategori bilah sisi '{label}'", "theme.DocSidebarItem.expandCategoryAriaLabel": "<PERSON><PERSON><PERSON> kategori bilah sisi '{label}'", "theme.ErrorPageContent.title": "<PERSON><PERSON><PERSON><PERSON>.", "theme.ErrorPageContent.tryAgain": "Coba kembali", "theme.NavBar.navAriaLabel": "<PERSON><PERSON><PERSON>", "theme.NotFound.p1": "<PERSON>mi tak dapat menemukan yang anda cari.", "theme.NotFound.p2": "<PERSON><PERSON>an hubungi pemilik situs yang mengarahkan anda ke URL asli dan beri tahu mereka bahwa tautan mereka salah.", "theme.NotFound.title": "Halaman Tak Ditemukan", "theme.TOCCollapsible.toggleButtonLabel": "Pada halaman ini", "theme.admonition.caution": "<PERSON><PERSON><PERSON>", "theme.admonition.danger": "bahaya", "theme.admonition.info": "info", "theme.admonition.note": "catatan", "theme.admonition.tip": "tip", "theme.admonition.warning": "peringatan", "theme.blog.archive.description": "<PERSON><PERSON><PERSON>", "theme.blog.archive.title": "<PERSON><PERSON><PERSON>", "theme.blog.author.noPosts": "This author has not written any posts yet.", "theme.blog.author.pageTitle": "{authorName} - {nPosts}", "theme.blog.authorsList.pageTitle": "Authors", "theme.blog.authorsList.viewAll": "View All Authors", "theme.blog.paginator.navAriaLabel": "Navigasi entri blog", "theme.blog.paginator.newerEntries": "<PERSON><PERSON> lebih baru", "theme.blog.paginator.olderEntries": "<PERSON><PERSON> le<PERSON> lama", "theme.blog.post.paginator.navAriaLabel": "Na<PERSON><PERSON>i halaman pos blog", "theme.blog.post.paginator.newerPost": "<PERSON><PERSON> lebih baru", "theme.blog.post.paginator.olderPost": "<PERSON><PERSON> lebih lama", "theme.blog.post.plurals": "Satu pos|{count} pos", "theme.blog.post.readMore": "Baca Selengkapnya", "theme.blog.post.readMoreLabel": "Baca selengkapnya mengenai {title}", "theme.blog.post.readingTime.plurals": "Satu menit membaca|{readingTime} menit membaca", "theme.blog.sidebar.navAriaLabel": "Navigasi pos blog terbaru", "theme.blog.tagTitle": "{nPosts} memiliki tag \"{tagName}\"", "theme.colorToggle.ariaLabel": "Ubah antara modus gelap dan modus terang (saat ini {mode})", "theme.colorToggle.ariaLabel.mode.dark": "modus gelap", "theme.colorToggle.ariaLabel.mode.light": "modus terang", "theme.colorToggle.ariaLabel.mode.system": "system mode", "theme.common.editThisPage": "Sunting halaman ini", "theme.common.headingLinkTitle": "<PERSON>t langsung ke {heading}", "theme.common.skipToMainContent": "<PERSON><PERSON> ke konten utama", "theme.contentVisibility.draftBanner.message": "This page is a draft. It will only be visible in dev and be excluded from the production build.", "theme.contentVisibility.draftBanner.title": "Draft page", "theme.contentVisibility.unlistedBanner.message": "Halaman ini tidak terdaftar. <PERSON>sin pencari tidak akan mengin<PERSON>, dan hanya pengguna yang memiliki tautan langsung yang dapat mengaksesnya.", "theme.contentVisibility.unlistedBanner.title": "Halaman tak terdaftar", "theme.docs.DocCard.categoryDescription.plurals": "1 butir|{count} butir", "theme.docs.breadcrumbs.home": "<PERSON><PERSON> utama", "theme.docs.breadcrumbs.navAriaLabel": "<PERSON><PERSON> navigasi", "theme.docs.paginator.navAriaLabel": "Halaman dokumentasi", "theme.docs.paginator.next": "Be<PERSON><PERSON>", "theme.docs.paginator.previous": "Sebelum", "theme.docs.sidebar.closeSidebarButtonAriaLabel": "<PERSON><PERSON>p bilah sisi", "theme.docs.sidebar.collapseButtonAriaLabel": "Ciutkan bilah sisi", "theme.docs.sidebar.collapseButtonTitle": "Ciutkan bilah sisi", "theme.docs.sidebar.expandButtonAriaLabel": "<PERSON><PERSON><PERSON> bilah sisi", "theme.docs.sidebar.expandButtonTitle": "<PERSON><PERSON><PERSON> bilah sisi", "theme.docs.sidebar.navAriaLabel": "<PERSON><PERSON>h sisi dokumentasi", "theme.docs.sidebar.toggleSidebarButtonAriaLabel": "<PERSON><PERSON><PERSON> bilah sisi", "theme.docs.tagDocListPageTitle": "{nDocsTagged} dengan \"{tagName}\"", "theme.docs.tagDocListPageTitle.nDocsTagged": "Satu dokumentasi memiliki tag|{count} dokumentasi memiliki tag", "theme.docs.versionBadge.label": "Versi: {versionLabel}", "theme.docs.versions.latestVersionLinkLabel": "versi terbaru", "theme.docs.versions.latestVersionSuggestionLabel": "Untuk dokumentasi terbaru, lihat {latestVersionLink} ({versionLabel}).", "theme.docs.versions.unmaintainedVersionLabel": "Ini adalah dokumentasi untuk {siteTitle} {versionLabel}, yang tidak lagi aktif di<PERSON>ola.", "theme.docs.versions.unreleasedVersionLabel": "Ini adalah dokumentasi yang belum dirilis untuk {siteTitle} {versionLabel}.", "theme.lastUpdated.atDate": " pada {date}", "theme.lastUpdated.byUser": " oleh {user}", "theme.lastUpdated.lastUpdatedAtBy": "Diperbaharui{atDate}{byUser}", "theme.navbar.mobileDropdown.collapseButton.collapseAriaLabel": "Collapse the dropdown", "theme.navbar.mobileDropdown.collapseButton.expandAriaLabel": "Expand the dropdown", "theme.navbar.mobileLanguageDropdown.label": "Bahasa", "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": "← Kembali ke menu utama", "theme.navbar.mobileVersionsDropdown.label": "<PERSON><PERSON><PERSON>", "theme.tags.tagsListLabel": "Tag:", "theme.tags.tagsPageLink": "<PERSON><PERSON>", "theme.tags.tagsPageTitle": "Tag"}