{"version": 3, "file": "react-router.min.js", "sources": ["../../node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "../../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "../../node_modules/react-is/cjs/react-is.production.min.js", "../../node_modules/react-is/index.js", "../../node_modules/object-assign/index.js", "../../node_modules/prop-types/factoryWithTypeCheckers.js", "../../node_modules/prop-types/factoryWithThrowingShims.js", "../../node_modules/prop-types/index.js", "../../node_modules/prop-types/lib/ReactPropTypesSecret.js", "../../node_modules/@babel/runtime/helpers/esm/extends.js", "../../node_modules/resolve-pathname/index.js", "../../node_modules/value-equal/index.js", "../../node_modules/tiny-invariant/dist/tiny-invariant.esm.js", "../../node_modules/history/esm/history.js", "../modules/miniCreateReactContext.js", "../modules/createNamedContext.js", "../modules/createContext.js", "../modules/HistoryContext.js", "../modules/RouterContext.js", "../modules/Router.js", "../modules/MemoryRouter.js", "../modules/Lifecycle.js", "../../node_modules/path-to-regexp/index.js", "../../node_modules/path-to-regexp/node_modules/isarray/index.js", "../modules/generatePath.js", "../modules/matchPath.js", "../modules/Route.js", "../../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../modules/StaticRouter.js", "../modules/Switch.js", "../../node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js", "../modules/hooks.js", "../modules/Prompt.js", "../modules/Redirect.js", "../modules/withRouter.js"], "sourcesContent": ["export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"@babel/runtime/helpers/esm/setPrototypeOf\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "/** @license React v16.9.0\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';Object.defineProperty(exports,\"__esModule\",{value:!0});\nvar b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,q=b?Symbol.for(\"react.suspense_list\"):\n60120,r=b?Symbol.for(\"react.memo\"):60115,t=b?Symbol.for(\"react.lazy\"):60116,v=b?Symbol.for(\"react.fundamental\"):60117,w=b?Symbol.for(\"react.responder\"):60118;function x(a){if(\"object\"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case h:return a;default:return u}}case t:case r:case d:return u}}}function y(a){return x(a)===m}exports.typeOf=x;exports.AsyncMode=l;\nexports.ConcurrentMode=m;exports.ContextConsumer=k;exports.ContextProvider=h;exports.Element=c;exports.ForwardRef=n;exports.Fragment=e;exports.Lazy=t;exports.Memo=r;exports.Portal=d;exports.Profiler=g;exports.StrictMode=f;exports.Suspense=p;\nexports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===v||a.$$typeof===w)};exports.isAsyncMode=function(a){return y(a)||x(a)===l};exports.isConcurrentMode=y;exports.isContextConsumer=function(a){return x(a)===k};exports.isContextProvider=function(a){return x(a)===h};\nexports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===c};exports.isForwardRef=function(a){return x(a)===n};exports.isFragment=function(a){return x(a)===e};exports.isLazy=function(a){return x(a)===t};exports.isMemo=function(a){return x(a)===r};exports.isPortal=function(a){return x(a)===d};exports.isProfiler=function(a){return x(a)===g};exports.isStrictMode=function(a){return x(a)===f};exports.isSuspense=function(a){return x(a)===p};\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar has = Function.call.bind(Object.prototype.hasOwnProperty);\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message) {\n    this.message = message;\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName  + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        if (checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret) == null) {\n          return null;\n        }\n      }\n\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (!checker) {\n          continue;\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from\n      // props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' +  JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n\nfunction emptyFunction() {}\nfunction emptyFunctionWithReset() {}\nemptyFunctionWithReset.resetWarningCache = emptyFunction;\n\nmodule.exports = function() {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n    var err = new Error(\n      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n      'Use PropTypes.checkPropTypes() to call them. ' +\n      'Read more at http://fb.me/use-check-prop-types'\n    );\n    err.name = 'Invariant Violation';\n    throw err;\n  };\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  };\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  var ReactPropTypes = {\n    array: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    elementType: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim,\n    exact: getShim,\n\n    checkPropTypes: emptyFunctionWithReset,\n    resetWarningCache: emptyFunction\n  };\n\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "export default function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}", "function isAbsolute(pathname) {\n  return pathname.charAt(0) === '/';\n}\n\n// About 1.5x faster than the two-arg version of Array#splice()\nfunction spliceOne(list, index) {\n  for (var i = index, k = i + 1, n = list.length; k < n; i += 1, k += 1) {\n    list[i] = list[k];\n  }\n\n  list.pop();\n}\n\n// This implementation is based heavily on node's url.parse\nfunction resolvePathname(to) {\n  var from = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n\n  var toParts = to && to.split('/') || [];\n  var fromParts = from && from.split('/') || [];\n\n  var isToAbs = to && isAbsolute(to);\n  var isFromAbs = from && isAbsolute(from);\n  var mustEndAbs = isToAbs || isFromAbs;\n\n  if (to && isAbsolute(to)) {\n    // to is absolute\n    fromParts = toParts;\n  } else if (toParts.length) {\n    // to is relative, drop the filename\n    fromParts.pop();\n    fromParts = fromParts.concat(toParts);\n  }\n\n  if (!fromParts.length) return '/';\n\n  var hasTrailingSlash = void 0;\n  if (fromParts.length) {\n    var last = fromParts[fromParts.length - 1];\n    hasTrailingSlash = last === '.' || last === '..' || last === '';\n  } else {\n    hasTrailingSlash = false;\n  }\n\n  var up = 0;\n  for (var i = fromParts.length; i >= 0; i--) {\n    var part = fromParts[i];\n\n    if (part === '.') {\n      spliceOne(fromParts, i);\n    } else if (part === '..') {\n      spliceOne(fromParts, i);\n      up++;\n    } else if (up) {\n      spliceOne(fromParts, i);\n      up--;\n    }\n  }\n\n  if (!mustEndAbs) for (; up--; up) {\n    fromParts.unshift('..');\n  }if (mustEndAbs && fromParts[0] !== '' && (!fromParts[0] || !isAbsolute(fromParts[0]))) fromParts.unshift('');\n\n  var result = fromParts.join('/');\n\n  if (hasTrailingSlash && result.substr(-1) !== '/') result += '/';\n\n  return result;\n}\n\nexport default resolvePathname;", "var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nfunction valueEqual(a, b) {\n  if (a === b) return true;\n\n  if (a == null || b == null) return false;\n\n  if (Array.isArray(a)) {\n    return Array.isArray(b) && a.length === b.length && a.every(function (item, index) {\n      return valueEqual(item, b[index]);\n    });\n  }\n\n  var aType = typeof a === 'undefined' ? 'undefined' : _typeof(a);\n  var bType = typeof b === 'undefined' ? 'undefined' : _typeof(b);\n\n  if (aType !== bType) return false;\n\n  if (aType === 'object') {\n    var aValue = a.valueOf();\n    var bValue = b.valueOf();\n\n    if (aValue !== a || bValue !== b) return valueEqual(aValue, bValue);\n\n    var aKeys = Object.keys(a);\n    var bKeys = Object.keys(b);\n\n    if (aKeys.length !== bKeys.length) return false;\n\n    return aKeys.every(function (key) {\n      return valueEqual(a[key], b[key]);\n    });\n  }\n\n  return false;\n}\n\nexport default valueEqual;", "var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n  if (condition) {\n    return;\n  }\n\n  if (isProduction) {\n    throw new Error(prefix);\n  } else {\n    throw new Error(prefix + \": \" + (message || ''));\n  }\n}\n\nexport default invariant;\n", "import _extends from '@babel/runtime/helpers/esm/extends';\nimport resolvePathname from 'resolve-pathname';\nimport valueEqual from 'value-equal';\nimport warning from 'tiny-warning';\nimport invariant from 'tiny-invariant';\n\nfunction addLeadingSlash(path) {\n  return path.charAt(0) === '/' ? path : '/' + path;\n}\nfunction stripLeadingSlash(path) {\n  return path.charAt(0) === '/' ? path.substr(1) : path;\n}\nfunction hasBasename(path, prefix) {\n  return new RegExp('^' + prefix + '(\\\\/|\\\\?|#|$)', 'i').test(path);\n}\nfunction stripBasename(path, prefix) {\n  return hasBasename(path, prefix) ? path.substr(prefix.length) : path;\n}\nfunction stripTrailingSlash(path) {\n  return path.charAt(path.length - 1) === '/' ? path.slice(0, -1) : path;\n}\nfunction parsePath(path) {\n  var pathname = path || '/';\n  var search = '';\n  var hash = '';\n  var hashIndex = pathname.indexOf('#');\n\n  if (hashIndex !== -1) {\n    hash = pathname.substr(hashIndex);\n    pathname = pathname.substr(0, hashIndex);\n  }\n\n  var searchIndex = pathname.indexOf('?');\n\n  if (searchIndex !== -1) {\n    search = pathname.substr(searchIndex);\n    pathname = pathname.substr(0, searchIndex);\n  }\n\n  return {\n    pathname: pathname,\n    search: search === '?' ? '' : search,\n    hash: hash === '#' ? '' : hash\n  };\n}\nfunction createPath(location) {\n  var pathname = location.pathname,\n      search = location.search,\n      hash = location.hash;\n  var path = pathname || '/';\n  if (search && search !== '?') path += search.charAt(0) === '?' ? search : \"?\" + search;\n  if (hash && hash !== '#') path += hash.charAt(0) === '#' ? hash : \"#\" + hash;\n  return path;\n}\n\nfunction createLocation(path, state, key, currentLocation) {\n  var location;\n\n  if (typeof path === 'string') {\n    // Two-arg form: push(path, state)\n    location = parsePath(path);\n    location.state = state;\n  } else {\n    // One-arg form: push(location)\n    location = _extends({}, path);\n    if (location.pathname === undefined) location.pathname = '';\n\n    if (location.search) {\n      if (location.search.charAt(0) !== '?') location.search = '?' + location.search;\n    } else {\n      location.search = '';\n    }\n\n    if (location.hash) {\n      if (location.hash.charAt(0) !== '#') location.hash = '#' + location.hash;\n    } else {\n      location.hash = '';\n    }\n\n    if (state !== undefined && location.state === undefined) location.state = state;\n  }\n\n  try {\n    location.pathname = decodeURI(location.pathname);\n  } catch (e) {\n    if (e instanceof URIError) {\n      throw new URIError('Pathname \"' + location.pathname + '\" could not be decoded. ' + 'This is likely caused by an invalid percent-encoding.');\n    } else {\n      throw e;\n    }\n  }\n\n  if (key) location.key = key;\n\n  if (currentLocation) {\n    // Resolve incomplete/relative pathname relative to current location.\n    if (!location.pathname) {\n      location.pathname = currentLocation.pathname;\n    } else if (location.pathname.charAt(0) !== '/') {\n      location.pathname = resolvePathname(location.pathname, currentLocation.pathname);\n    }\n  } else {\n    // When there is no prior location and pathname is empty, set it to /\n    if (!location.pathname) {\n      location.pathname = '/';\n    }\n  }\n\n  return location;\n}\nfunction locationsAreEqual(a, b) {\n  return a.pathname === b.pathname && a.search === b.search && a.hash === b.hash && a.key === b.key && valueEqual(a.state, b.state);\n}\n\nfunction createTransitionManager() {\n  var prompt = null;\n\n  function setPrompt(nextPrompt) {\n    process.env.NODE_ENV !== \"production\" ? warning(prompt == null, 'A history supports only one prompt at a time') : void 0;\n    prompt = nextPrompt;\n    return function () {\n      if (prompt === nextPrompt) prompt = null;\n    };\n  }\n\n  function confirmTransitionTo(location, action, getUserConfirmation, callback) {\n    // TODO: If another transition starts while we're still confirming\n    // the previous one, we may end up in a weird state. Figure out the\n    // best way to handle this.\n    if (prompt != null) {\n      var result = typeof prompt === 'function' ? prompt(location, action) : prompt;\n\n      if (typeof result === 'string') {\n        if (typeof getUserConfirmation === 'function') {\n          getUserConfirmation(result, callback);\n        } else {\n          process.env.NODE_ENV !== \"production\" ? warning(false, 'A history needs a getUserConfirmation function in order to use a prompt message') : void 0;\n          callback(true);\n        }\n      } else {\n        // Return false from a transition hook to cancel the transition.\n        callback(result !== false);\n      }\n    } else {\n      callback(true);\n    }\n  }\n\n  var listeners = [];\n\n  function appendListener(fn) {\n    var isActive = true;\n\n    function listener() {\n      if (isActive) fn.apply(void 0, arguments);\n    }\n\n    listeners.push(listener);\n    return function () {\n      isActive = false;\n      listeners = listeners.filter(function (item) {\n        return item !== listener;\n      });\n    };\n  }\n\n  function notifyListeners() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    listeners.forEach(function (listener) {\n      return listener.apply(void 0, args);\n    });\n  }\n\n  return {\n    setPrompt: setPrompt,\n    confirmTransitionTo: confirmTransitionTo,\n    appendListener: appendListener,\n    notifyListeners: notifyListeners\n  };\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nfunction getConfirmation(message, callback) {\n  callback(window.confirm(message)); // eslint-disable-line no-alert\n}\n/**\n * Returns true if the HTML5 history API is supported. Taken from Modernizr.\n *\n * https://github.com/Modernizr/Modernizr/blob/master/LICENSE\n * https://github.com/Modernizr/Modernizr/blob/master/feature-detects/history.js\n * changed to avoid false negatives for Windows Phones: https://github.com/reactjs/react-router/issues/586\n */\n\nfunction supportsHistory() {\n  var ua = window.navigator.userAgent;\n  if ((ua.indexOf('Android 2.') !== -1 || ua.indexOf('Android 4.0') !== -1) && ua.indexOf('Mobile Safari') !== -1 && ua.indexOf('Chrome') === -1 && ua.indexOf('Windows Phone') === -1) return false;\n  return window.history && 'pushState' in window.history;\n}\n/**\n * Returns true if browser fires popstate on hash change.\n * IE10 and IE11 do not.\n */\n\nfunction supportsPopStateOnHashChange() {\n  return window.navigator.userAgent.indexOf('Trident') === -1;\n}\n/**\n * Returns false if using go(n) with hash history causes a full page reload.\n */\n\nfunction supportsGoWithoutReloadUsingHash() {\n  return window.navigator.userAgent.indexOf('Firefox') === -1;\n}\n/**\n * Returns true if a given popstate event is an extraneous WebKit event.\n * Accounts for the fact that Chrome on iOS fires real popstate events\n * containing undefined state when pressing the back button.\n */\n\nfunction isExtraneousPopstateEvent(event) {\n  event.state === undefined && navigator.userAgent.indexOf('CriOS') === -1;\n}\n\nvar PopStateEvent = 'popstate';\nvar HashChangeEvent = 'hashchange';\n\nfunction getHistoryState() {\n  try {\n    return window.history.state || {};\n  } catch (e) {\n    // IE 11 sometimes throws when accessing window.history.state\n    // See https://github.com/ReactTraining/history/pull/289\n    return {};\n  }\n}\n/**\n * Creates a history object that uses the HTML5 history API including\n * pushState, replaceState, and the popstate event.\n */\n\n\nfunction createBrowserHistory(props) {\n  if (props === void 0) {\n    props = {};\n  }\n\n  !canUseDOM ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Browser history needs a DOM') : invariant(false) : void 0;\n  var globalHistory = window.history;\n  var canUseHistory = supportsHistory();\n  var needsHashChangeListener = !supportsPopStateOnHashChange();\n  var _props = props,\n      _props$forceRefresh = _props.forceRefresh,\n      forceRefresh = _props$forceRefresh === void 0 ? false : _props$forceRefresh,\n      _props$getUserConfirm = _props.getUserConfirmation,\n      getUserConfirmation = _props$getUserConfirm === void 0 ? getConfirmation : _props$getUserConfirm,\n      _props$keyLength = _props.keyLength,\n      keyLength = _props$keyLength === void 0 ? 6 : _props$keyLength;\n  var basename = props.basename ? stripTrailingSlash(addLeadingSlash(props.basename)) : '';\n\n  function getDOMLocation(historyState) {\n    var _ref = historyState || {},\n        key = _ref.key,\n        state = _ref.state;\n\n    var _window$location = window.location,\n        pathname = _window$location.pathname,\n        search = _window$location.search,\n        hash = _window$location.hash;\n    var path = pathname + search + hash;\n    process.env.NODE_ENV !== \"production\" ? warning(!basename || hasBasename(path, basename), 'You are attempting to use a basename on a page whose URL path does not begin ' + 'with the basename. Expected path \"' + path + '\" to begin with \"' + basename + '\".') : void 0;\n    if (basename) path = stripBasename(path, basename);\n    return createLocation(path, state, key);\n  }\n\n  function createKey() {\n    return Math.random().toString(36).substr(2, keyLength);\n  }\n\n  var transitionManager = createTransitionManager();\n\n  function setState(nextState) {\n    _extends(history, nextState);\n\n    history.length = globalHistory.length;\n    transitionManager.notifyListeners(history.location, history.action);\n  }\n\n  function handlePopState(event) {\n    // Ignore extraneous popstate events in WebKit.\n    if (isExtraneousPopstateEvent(event)) return;\n    handlePop(getDOMLocation(event.state));\n  }\n\n  function handleHashChange() {\n    handlePop(getDOMLocation(getHistoryState()));\n  }\n\n  var forceNextPop = false;\n\n  function handlePop(location) {\n    if (forceNextPop) {\n      forceNextPop = false;\n      setState();\n    } else {\n      var action = 'POP';\n      transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n        if (ok) {\n          setState({\n            action: action,\n            location: location\n          });\n        } else {\n          revertPop(location);\n        }\n      });\n    }\n  }\n\n  function revertPop(fromLocation) {\n    var toLocation = history.location; // TODO: We could probably make this more reliable by\n    // keeping a list of keys we've seen in sessionStorage.\n    // Instead, we just default to 0 for keys we don't know.\n\n    var toIndex = allKeys.indexOf(toLocation.key);\n    if (toIndex === -1) toIndex = 0;\n    var fromIndex = allKeys.indexOf(fromLocation.key);\n    if (fromIndex === -1) fromIndex = 0;\n    var delta = toIndex - fromIndex;\n\n    if (delta) {\n      forceNextPop = true;\n      go(delta);\n    }\n  }\n\n  var initialLocation = getDOMLocation(getHistoryState());\n  var allKeys = [initialLocation.key]; // Public interface\n\n  function createHref(location) {\n    return basename + createPath(location);\n  }\n\n  function push(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to push when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'PUSH';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var href = createHref(location);\n      var key = location.key,\n          state = location.state;\n\n      if (canUseHistory) {\n        globalHistory.pushState({\n          key: key,\n          state: state\n        }, null, href);\n\n        if (forceRefresh) {\n          window.location.href = href;\n        } else {\n          var prevIndex = allKeys.indexOf(history.location.key);\n          var nextKeys = allKeys.slice(0, prevIndex === -1 ? 0 : prevIndex + 1);\n          nextKeys.push(location.key);\n          allKeys = nextKeys;\n          setState({\n            action: action,\n            location: location\n          });\n        }\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Browser history cannot push state in browsers that do not support HTML5 history') : void 0;\n        window.location.href = href;\n      }\n    });\n  }\n\n  function replace(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to replace when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'REPLACE';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var href = createHref(location);\n      var key = location.key,\n          state = location.state;\n\n      if (canUseHistory) {\n        globalHistory.replaceState({\n          key: key,\n          state: state\n        }, null, href);\n\n        if (forceRefresh) {\n          window.location.replace(href);\n        } else {\n          var prevIndex = allKeys.indexOf(history.location.key);\n          if (prevIndex !== -1) allKeys[prevIndex] = location.key;\n          setState({\n            action: action,\n            location: location\n          });\n        }\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Browser history cannot replace state in browsers that do not support HTML5 history') : void 0;\n        window.location.replace(href);\n      }\n    });\n  }\n\n  function go(n) {\n    globalHistory.go(n);\n  }\n\n  function goBack() {\n    go(-1);\n  }\n\n  function goForward() {\n    go(1);\n  }\n\n  var listenerCount = 0;\n\n  function checkDOMListeners(delta) {\n    listenerCount += delta;\n\n    if (listenerCount === 1 && delta === 1) {\n      window.addEventListener(PopStateEvent, handlePopState);\n      if (needsHashChangeListener) window.addEventListener(HashChangeEvent, handleHashChange);\n    } else if (listenerCount === 0) {\n      window.removeEventListener(PopStateEvent, handlePopState);\n      if (needsHashChangeListener) window.removeEventListener(HashChangeEvent, handleHashChange);\n    }\n  }\n\n  var isBlocked = false;\n\n  function block(prompt) {\n    if (prompt === void 0) {\n      prompt = false;\n    }\n\n    var unblock = transitionManager.setPrompt(prompt);\n\n    if (!isBlocked) {\n      checkDOMListeners(1);\n      isBlocked = true;\n    }\n\n    return function () {\n      if (isBlocked) {\n        isBlocked = false;\n        checkDOMListeners(-1);\n      }\n\n      return unblock();\n    };\n  }\n\n  function listen(listener) {\n    var unlisten = transitionManager.appendListener(listener);\n    checkDOMListeners(1);\n    return function () {\n      checkDOMListeners(-1);\n      unlisten();\n    };\n  }\n\n  var history = {\n    length: globalHistory.length,\n    action: 'POP',\n    location: initialLocation,\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    goBack: goBack,\n    goForward: goForward,\n    block: block,\n    listen: listen\n  };\n  return history;\n}\n\nvar HashChangeEvent$1 = 'hashchange';\nvar HashPathCoders = {\n  hashbang: {\n    encodePath: function encodePath(path) {\n      return path.charAt(0) === '!' ? path : '!/' + stripLeadingSlash(path);\n    },\n    decodePath: function decodePath(path) {\n      return path.charAt(0) === '!' ? path.substr(1) : path;\n    }\n  },\n  noslash: {\n    encodePath: stripLeadingSlash,\n    decodePath: addLeadingSlash\n  },\n  slash: {\n    encodePath: addLeadingSlash,\n    decodePath: addLeadingSlash\n  }\n};\n\nfunction getHashPath() {\n  // We can't use window.location.hash here because it's not\n  // consistent across browsers - Firefox will pre-decode it!\n  var href = window.location.href;\n  var hashIndex = href.indexOf('#');\n  return hashIndex === -1 ? '' : href.substring(hashIndex + 1);\n}\n\nfunction pushHashPath(path) {\n  window.location.hash = path;\n}\n\nfunction replaceHashPath(path) {\n  var hashIndex = window.location.href.indexOf('#');\n  window.location.replace(window.location.href.slice(0, hashIndex >= 0 ? hashIndex : 0) + '#' + path);\n}\n\nfunction createHashHistory(props) {\n  if (props === void 0) {\n    props = {};\n  }\n\n  !canUseDOM ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Hash history needs a DOM') : invariant(false) : void 0;\n  var globalHistory = window.history;\n  var canGoWithoutReload = supportsGoWithoutReloadUsingHash();\n  var _props = props,\n      _props$getUserConfirm = _props.getUserConfirmation,\n      getUserConfirmation = _props$getUserConfirm === void 0 ? getConfirmation : _props$getUserConfirm,\n      _props$hashType = _props.hashType,\n      hashType = _props$hashType === void 0 ? 'slash' : _props$hashType;\n  var basename = props.basename ? stripTrailingSlash(addLeadingSlash(props.basename)) : '';\n  var _HashPathCoders$hashT = HashPathCoders[hashType],\n      encodePath = _HashPathCoders$hashT.encodePath,\n      decodePath = _HashPathCoders$hashT.decodePath;\n\n  function getDOMLocation() {\n    var path = decodePath(getHashPath());\n    process.env.NODE_ENV !== \"production\" ? warning(!basename || hasBasename(path, basename), 'You are attempting to use a basename on a page whose URL path does not begin ' + 'with the basename. Expected path \"' + path + '\" to begin with \"' + basename + '\".') : void 0;\n    if (basename) path = stripBasename(path, basename);\n    return createLocation(path);\n  }\n\n  var transitionManager = createTransitionManager();\n\n  function setState(nextState) {\n    _extends(history, nextState);\n\n    history.length = globalHistory.length;\n    transitionManager.notifyListeners(history.location, history.action);\n  }\n\n  var forceNextPop = false;\n  var ignorePath = null;\n\n  function handleHashChange() {\n    var path = getHashPath();\n    var encodedPath = encodePath(path);\n\n    if (path !== encodedPath) {\n      // Ensure we always have a properly-encoded hash.\n      replaceHashPath(encodedPath);\n    } else {\n      var location = getDOMLocation();\n      var prevLocation = history.location;\n      if (!forceNextPop && locationsAreEqual(prevLocation, location)) return; // A hashchange doesn't always == location change.\n\n      if (ignorePath === createPath(location)) return; // Ignore this change; we already setState in push/replace.\n\n      ignorePath = null;\n      handlePop(location);\n    }\n  }\n\n  function handlePop(location) {\n    if (forceNextPop) {\n      forceNextPop = false;\n      setState();\n    } else {\n      var action = 'POP';\n      transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n        if (ok) {\n          setState({\n            action: action,\n            location: location\n          });\n        } else {\n          revertPop(location);\n        }\n      });\n    }\n  }\n\n  function revertPop(fromLocation) {\n    var toLocation = history.location; // TODO: We could probably make this more reliable by\n    // keeping a list of paths we've seen in sessionStorage.\n    // Instead, we just default to 0 for paths we don't know.\n\n    var toIndex = allPaths.lastIndexOf(createPath(toLocation));\n    if (toIndex === -1) toIndex = 0;\n    var fromIndex = allPaths.lastIndexOf(createPath(fromLocation));\n    if (fromIndex === -1) fromIndex = 0;\n    var delta = toIndex - fromIndex;\n\n    if (delta) {\n      forceNextPop = true;\n      go(delta);\n    }\n  } // Ensure the hash is encoded properly before doing anything else.\n\n\n  var path = getHashPath();\n  var encodedPath = encodePath(path);\n  if (path !== encodedPath) replaceHashPath(encodedPath);\n  var initialLocation = getDOMLocation();\n  var allPaths = [createPath(initialLocation)]; // Public interface\n\n  function createHref(location) {\n    return '#' + encodePath(basename + createPath(location));\n  }\n\n  function push(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Hash history cannot push state; it is ignored') : void 0;\n    var action = 'PUSH';\n    var location = createLocation(path, undefined, undefined, history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var path = createPath(location);\n      var encodedPath = encodePath(basename + path);\n      var hashChanged = getHashPath() !== encodedPath;\n\n      if (hashChanged) {\n        // We cannot tell if a hashchange was caused by a PUSH, so we'd\n        // rather setState here and ignore the hashchange. The caveat here\n        // is that other hash histories in the page will consider it a POP.\n        ignorePath = path;\n        pushHashPath(encodedPath);\n        var prevIndex = allPaths.lastIndexOf(createPath(history.location));\n        var nextPaths = allPaths.slice(0, prevIndex === -1 ? 0 : prevIndex + 1);\n        nextPaths.push(path);\n        allPaths = nextPaths;\n        setState({\n          action: action,\n          location: location\n        });\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(false, 'Hash history cannot PUSH the same path; a new entry will not be added to the history stack') : void 0;\n        setState();\n      }\n    });\n  }\n\n  function replace(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Hash history cannot replace state; it is ignored') : void 0;\n    var action = 'REPLACE';\n    var location = createLocation(path, undefined, undefined, history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var path = createPath(location);\n      var encodedPath = encodePath(basename + path);\n      var hashChanged = getHashPath() !== encodedPath;\n\n      if (hashChanged) {\n        // We cannot tell if a hashchange was caused by a REPLACE, so we'd\n        // rather setState here and ignore the hashchange. The caveat here\n        // is that other hash histories in the page will consider it a POP.\n        ignorePath = path;\n        replaceHashPath(encodedPath);\n      }\n\n      var prevIndex = allPaths.indexOf(createPath(history.location));\n      if (prevIndex !== -1) allPaths[prevIndex] = path;\n      setState({\n        action: action,\n        location: location\n      });\n    });\n  }\n\n  function go(n) {\n    process.env.NODE_ENV !== \"production\" ? warning(canGoWithoutReload, 'Hash history go(n) causes a full page reload in this browser') : void 0;\n    globalHistory.go(n);\n  }\n\n  function goBack() {\n    go(-1);\n  }\n\n  function goForward() {\n    go(1);\n  }\n\n  var listenerCount = 0;\n\n  function checkDOMListeners(delta) {\n    listenerCount += delta;\n\n    if (listenerCount === 1 && delta === 1) {\n      window.addEventListener(HashChangeEvent$1, handleHashChange);\n    } else if (listenerCount === 0) {\n      window.removeEventListener(HashChangeEvent$1, handleHashChange);\n    }\n  }\n\n  var isBlocked = false;\n\n  function block(prompt) {\n    if (prompt === void 0) {\n      prompt = false;\n    }\n\n    var unblock = transitionManager.setPrompt(prompt);\n\n    if (!isBlocked) {\n      checkDOMListeners(1);\n      isBlocked = true;\n    }\n\n    return function () {\n      if (isBlocked) {\n        isBlocked = false;\n        checkDOMListeners(-1);\n      }\n\n      return unblock();\n    };\n  }\n\n  function listen(listener) {\n    var unlisten = transitionManager.appendListener(listener);\n    checkDOMListeners(1);\n    return function () {\n      checkDOMListeners(-1);\n      unlisten();\n    };\n  }\n\n  var history = {\n    length: globalHistory.length,\n    action: 'POP',\n    location: initialLocation,\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    goBack: goBack,\n    goForward: goForward,\n    block: block,\n    listen: listen\n  };\n  return history;\n}\n\nfunction clamp(n, lowerBound, upperBound) {\n  return Math.min(Math.max(n, lowerBound), upperBound);\n}\n/**\n * Creates a history object that stores locations in memory.\n */\n\n\nfunction createMemoryHistory(props) {\n  if (props === void 0) {\n    props = {};\n  }\n\n  var _props = props,\n      getUserConfirmation = _props.getUserConfirmation,\n      _props$initialEntries = _props.initialEntries,\n      initialEntries = _props$initialEntries === void 0 ? ['/'] : _props$initialEntries,\n      _props$initialIndex = _props.initialIndex,\n      initialIndex = _props$initialIndex === void 0 ? 0 : _props$initialIndex,\n      _props$keyLength = _props.keyLength,\n      keyLength = _props$keyLength === void 0 ? 6 : _props$keyLength;\n  var transitionManager = createTransitionManager();\n\n  function setState(nextState) {\n    _extends(history, nextState);\n\n    history.length = history.entries.length;\n    transitionManager.notifyListeners(history.location, history.action);\n  }\n\n  function createKey() {\n    return Math.random().toString(36).substr(2, keyLength);\n  }\n\n  var index = clamp(initialIndex, 0, initialEntries.length - 1);\n  var entries = initialEntries.map(function (entry) {\n    return typeof entry === 'string' ? createLocation(entry, undefined, createKey()) : createLocation(entry, undefined, entry.key || createKey());\n  }); // Public interface\n\n  var createHref = createPath;\n\n  function push(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to push when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'PUSH';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var prevIndex = history.index;\n      var nextIndex = prevIndex + 1;\n      var nextEntries = history.entries.slice(0);\n\n      if (nextEntries.length > nextIndex) {\n        nextEntries.splice(nextIndex, nextEntries.length - nextIndex, location);\n      } else {\n        nextEntries.push(location);\n      }\n\n      setState({\n        action: action,\n        location: location,\n        index: nextIndex,\n        entries: nextEntries\n      });\n    });\n  }\n\n  function replace(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to replace when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'REPLACE';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      history.entries[history.index] = location;\n      setState({\n        action: action,\n        location: location\n      });\n    });\n  }\n\n  function go(n) {\n    var nextIndex = clamp(history.index + n, 0, history.entries.length - 1);\n    var action = 'POP';\n    var location = history.entries[nextIndex];\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (ok) {\n        setState({\n          action: action,\n          location: location,\n          index: nextIndex\n        });\n      } else {\n        // Mimic the behavior of DOM histories by\n        // causing a render after a cancelled POP.\n        setState();\n      }\n    });\n  }\n\n  function goBack() {\n    go(-1);\n  }\n\n  function goForward() {\n    go(1);\n  }\n\n  function canGo(n) {\n    var nextIndex = history.index + n;\n    return nextIndex >= 0 && nextIndex < history.entries.length;\n  }\n\n  function block(prompt) {\n    if (prompt === void 0) {\n      prompt = false;\n    }\n\n    return transitionManager.setPrompt(prompt);\n  }\n\n  function listen(listener) {\n    return transitionManager.appendListener(listener);\n  }\n\n  var history = {\n    length: entries.length,\n    action: 'POP',\n    location: entries[index],\n    index: index,\n    entries: entries,\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    goBack: goBack,\n    goForward: goForward,\n    canGo: canGo,\n    block: block,\n    listen: listen\n  };\n  return history;\n}\n\nexport { createBrowserHistory, createHashHistory, createMemoryHistory, createLocation, locationsAreEqual, parsePath, createPath };\n", "// MIT License\n// Copyright (c) 2019-present StringEpsilon <<EMAIL>>\n// Copyright (c) 2017-2019 <PERSON> <<EMAIL>>\n// https://github.com/StringEpsilon/mini-create-react-context\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\nimport warning from \"tiny-warning\";\n\nconst MAX_SIGNED_31_BIT_INT = **********;\n\nconst commonjsGlobal =\n  typeof globalThis !== \"undefined\" // 'global proper'\n    ? // eslint-disable-next-line no-undef\n      globalThis\n    : typeof window !== \"undefined\"\n    ? window // Browser\n    : typeof global !== \"undefined\"\n    ? global // node.js\n    : {};\n\nfunction getUniqueId() {\n  let key = \"__global_unique_id__\";\n  return (commonjsGlobal[key] = (commonjsGlobal[key] || 0) + 1);\n}\n\n// Inlined Object.is polyfill.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\nfunction objectIs(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    // eslint-disable-next-line no-self-compare\n    return x !== x && y !== y;\n  }\n}\n\nfunction createEventEmitter(value) {\n  let handlers = [];\n  return {\n    on(handler) {\n      handlers.push(handler);\n    },\n\n    off(handler) {\n      handlers = handlers.filter(h => h !== handler);\n    },\n\n    get() {\n      return value;\n    },\n\n    set(newValue, changedBits) {\n      value = newValue;\n      handlers.forEach(handler => handler(value, changedBits));\n    }\n  };\n}\n\nfunction onlyChild(children) {\n  return Array.isArray(children) ? children[0] : children;\n}\n\nexport default function createReactContext(defaultValue, calculateChangedBits) {\n  const contextProp = \"__create-react-context-\" + getUniqueId() + \"__\";\n\n  class Provider extends React.Component {\n    emitter = createEventEmitter(this.props.value);\n\n    static childContextTypes = {\n      [contextProp]: PropTypes.object.isRequired\n    };\n\n    getChildContext() {\n      return {\n        [contextProp]: this.emitter\n      };\n    }\n\n    componentWillReceiveProps(nextProps) {\n      if (this.props.value !== nextProps.value) {\n        let oldValue = this.props.value;\n        let newValue = nextProps.value;\n        let changedBits;\n\n        if (objectIs(oldValue, newValue)) {\n          changedBits = 0; // No change\n        } else {\n          changedBits =\n            typeof calculateChangedBits === \"function\"\n              ? calculateChangedBits(oldValue, newValue)\n              : MAX_SIGNED_31_BIT_INT;\n          if (process.env.NODE_ENV !== \"production\") {\n            warning(\n              (changedBits & MAX_SIGNED_31_BIT_INT) === changedBits,\n              \"calculateChangedBits: Expected the return value to be a \" +\n                \"31-bit integer. Instead received: \" +\n                changedBits\n            );\n          }\n\n          changedBits |= 0;\n\n          if (changedBits !== 0) {\n            this.emitter.set(nextProps.value, changedBits);\n          }\n        }\n      }\n    }\n\n    render() {\n      return this.props.children;\n    }\n  }\n\n  class Consumer extends React.Component {\n    static contextTypes = {\n      [contextProp]: PropTypes.object\n    };\n\n    observedBits;\n\n    state = {\n      value: this.getValue()\n    };\n\n    componentWillReceiveProps(nextProps) {\n      let { observedBits } = nextProps;\n      this.observedBits =\n        observedBits === undefined || observedBits === null\n          ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n          : observedBits;\n    }\n\n    componentDidMount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].on(this.onUpdate);\n      }\n      let { observedBits } = this.props;\n      this.observedBits =\n        observedBits === undefined || observedBits === null\n          ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n          : observedBits;\n    }\n\n    componentWillUnmount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].off(this.onUpdate);\n      }\n    }\n\n    getValue() {\n      if (this.context[contextProp]) {\n        return this.context[contextProp].get();\n      } else {\n        return defaultValue;\n      }\n    }\n\n    onUpdate = (newValue, changedBits) => {\n      const observedBits = this.observedBits | 0;\n      if ((observedBits & changedBits) !== 0) {\n        this.setState({ value: this.getValue() });\n      }\n    };\n\n    render() {\n      return onlyChild(this.props.children)(this.state.value);\n    }\n  }\n\n  return {\n    Provider,\n    Consumer\n  };\n}\n", "// TODO: Replace with React.createContext once we can assume React 16+\nimport createContext from \"./createContext\";\n\nconst createNamedContext = name => {\n  const context = createContext();\n  context.displayName = name;\n\n  return context;\n};\n\nexport default createNamedContext;\n", "// MIT License\n// Copyright (c) 2019-present StringEpsilon <<EMAIL>>\n// Copyright (c) 2017-2019 <PERSON> <<EMAIL>>\n// https://github.com/StringEpsilon/mini-create-react-context\nimport React from \"react\";\nimport createReactContext from \"./miniCreateReactContext\";\n\nexport default React.createContext || createReactContext;\n", "import createNamedContext from \"./createNamedContext\";\n\nconst historyContext = /*#__PURE__*/ createNamedContext(\"Router-History\");\nexport default historyContext;\n", "import createNamedContext from \"./createNamedContext\";\n\nconst context = /*#__PURE__*/ createNamedContext(\"Router\");\nexport default context;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport warning from \"tiny-warning\";\n\nimport HistoryContext from \"./HistoryContext.js\";\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * The public API for putting history on context.\n */\nclass Router extends React.Component {\n  static computeRootMatch(pathname) {\n    return { path: \"/\", url: \"/\", params: {}, isExact: pathname === \"/\" };\n  }\n\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      location: props.history.location\n    };\n\n    // This is a bit of a hack. We have to start listening for location\n    // changes here in the constructor in case there are any <Redirect>s\n    // on the initial render. If there are, they will replace/push when\n    // they mount and since cDM fires in children before parents, we may\n    // get a new location before the <Router> is mounted.\n    this._isMounted = false;\n    this._pendingLocation = null;\n\n    if (!props.staticContext) {\n      this.unlisten = props.history.listen(location => {\n        this._pendingLocation = location;\n      });\n    }\n  }\n\n  componentDidMount() {\n    this._isMounted = true;\n\n    if (this.unlisten) {\n      // Any pre-mount location changes have been captured at\n      // this point, so unregister the listener.\n      this.unlisten();\n    }\n    if (!this.props.staticContext) {\n      this.unlisten = this.props.history.listen(location => {\n        if (this._isMounted) {\n          this.setState({ location });\n        }\n      });\n    }\n    if (this._pendingLocation) {\n      this.setState({ location: this._pendingLocation });\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.unlisten) {\n      this.unlisten();\n      this._isMounted = false;\n      this._pendingLocation = null;\n    }\n  }\n\n  render() {\n    return (\n      <RouterContext.Provider\n        value={{\n          history: this.props.history,\n          location: this.state.location,\n          match: Router.computeRootMatch(this.state.location.pathname),\n          staticContext: this.props.staticContext\n        }}\n      >\n        <HistoryContext.Provider\n          children={this.props.children || null}\n          value={this.props.history}\n        />\n      </RouterContext.Provider>\n    );\n  }\n}\n\nif (__DEV__) {\n  Router.propTypes = {\n    children: PropTypes.node,\n    history: PropTypes.object.isRequired,\n    staticContext: PropTypes.object\n  };\n\n  Router.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      prevProps.history === this.props.history,\n      \"You cannot change <Router history>\"\n    );\n  };\n}\n\nexport default Router;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createMemoryHistory as createHistory } from \"history\";\nimport warning from \"tiny-warning\";\n\nimport Router from \"./Router.js\";\n\n/**\n * The public API for a <Router> that stores location in memory.\n */\nclass MemoryRouter extends React.Component {\n  history = createHistory(this.props);\n\n  render() {\n    return <Router history={this.history} children={this.props.children} />;\n  }\n}\n\nif (__DEV__) {\n  MemoryRouter.propTypes = {\n    initialEntries: PropTypes.array,\n    initialIndex: PropTypes.number,\n    getUserConfirmation: PropTypes.func,\n    keyLength: PropTypes.number,\n    children: PropTypes.node\n  };\n\n  MemoryRouter.prototype.componentDidMount = function() {\n    warning(\n      !this.props.history,\n      \"<MemoryRouter> ignores the history prop. To use a custom history, \" +\n        \"use `import { Router }` instead of `import { MemoryRouter as Router }`.\"\n    );\n  };\n}\n\nexport default MemoryRouter;\n", "import React from \"react\";\n\nclass Lifecycle extends React.Component {\n  componentDidMount() {\n    if (this.props.onMount) this.props.onMount.call(this, this);\n  }\n\n  componentDidUpdate(prevProps) {\n    if (this.props.onUpdate) this.props.onUpdate.call(this, this, prevProps);\n  }\n\n  componentWillUnmount() {\n    if (this.props.onUnmount) this.props.onUnmount.call(this, this);\n  }\n\n  render() {\n    return null;\n  }\n}\n\nexport default Lifecycle;\n", "var isarray = require('isarray')\n\n/**\n * Expose `pathToRegexp`.\n */\nmodule.exports = pathToRegexp\nmodule.exports.parse = parse\nmodule.exports.compile = compile\nmodule.exports.tokensToFunction = tokensToFunction\nmodule.exports.tokensToRegExp = tokensToRegExp\n\n/**\n * The main path matching regexp utility.\n *\n * @type {RegExp}\n */\nvar PATH_REGEXP = new RegExp([\n  // Match escaped characters that would otherwise appear in future matches.\n  // This allows the user to escape special characters that won't transform.\n  '(\\\\\\\\.)',\n  // Match Express-style parameters and un-named parameters with a prefix\n  // and optional suffixes. Matches appear as:\n  //\n  // \"/:test(\\\\d+)?\" => [\"/\", \"test\", \"\\d+\", undefined, \"?\", undefined]\n  // \"/route(\\\\d+)\"  => [undefined, undefined, undefined, \"\\d+\", undefined, undefined]\n  // \"/*\"            => [\"/\", undefined, undefined, undefined, undefined, \"*\"]\n  '([\\\\/.])?(?:(?:\\\\:(\\\\w+)(?:\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))?|\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))([+*?])?|(\\\\*))'\n].join('|'), 'g')\n\n/**\n * Parse a string for the raw tokens.\n *\n * @param  {string}  str\n * @param  {Object=} options\n * @return {!Array}\n */\nfunction parse (str, options) {\n  var tokens = []\n  var key = 0\n  var index = 0\n  var path = ''\n  var defaultDelimiter = options && options.delimiter || '/'\n  var res\n\n  while ((res = PATH_REGEXP.exec(str)) != null) {\n    var m = res[0]\n    var escaped = res[1]\n    var offset = res.index\n    path += str.slice(index, offset)\n    index = offset + m.length\n\n    // Ignore already escaped sequences.\n    if (escaped) {\n      path += escaped[1]\n      continue\n    }\n\n    var next = str[index]\n    var prefix = res[2]\n    var name = res[3]\n    var capture = res[4]\n    var group = res[5]\n    var modifier = res[6]\n    var asterisk = res[7]\n\n    // Push the current path onto the tokens.\n    if (path) {\n      tokens.push(path)\n      path = ''\n    }\n\n    var partial = prefix != null && next != null && next !== prefix\n    var repeat = modifier === '+' || modifier === '*'\n    var optional = modifier === '?' || modifier === '*'\n    var delimiter = res[2] || defaultDelimiter\n    var pattern = capture || group\n\n    tokens.push({\n      name: name || key++,\n      prefix: prefix || '',\n      delimiter: delimiter,\n      optional: optional,\n      repeat: repeat,\n      partial: partial,\n      asterisk: !!asterisk,\n      pattern: pattern ? escapeGroup(pattern) : (asterisk ? '.*' : '[^' + escapeString(delimiter) + ']+?')\n    })\n  }\n\n  // Match any characters still remaining.\n  if (index < str.length) {\n    path += str.substr(index)\n  }\n\n  // If the path exists, push it onto the end.\n  if (path) {\n    tokens.push(path)\n  }\n\n  return tokens\n}\n\n/**\n * Compile a string to a template function for the path.\n *\n * @param  {string}             str\n * @param  {Object=}            options\n * @return {!function(Object=, Object=)}\n */\nfunction compile (str, options) {\n  return tokensToFunction(parse(str, options))\n}\n\n/**\n * Prettier encoding of URI path segments.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeURIComponentPretty (str) {\n  return encodeURI(str).replace(/[\\/?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\n/**\n * Encode the asterisk parameter. Similar to `pretty`, but allows slashes.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeAsterisk (str) {\n  return encodeURI(str).replace(/[?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nfunction tokensToFunction (tokens) {\n  // Compile all the tokens into regexps.\n  var matches = new Array(tokens.length)\n\n  // Compile all the patterns before compilation.\n  for (var i = 0; i < tokens.length; i++) {\n    if (typeof tokens[i] === 'object') {\n      matches[i] = new RegExp('^(?:' + tokens[i].pattern + ')$')\n    }\n  }\n\n  return function (obj, opts) {\n    var path = ''\n    var data = obj || {}\n    var options = opts || {}\n    var encode = options.pretty ? encodeURIComponentPretty : encodeURIComponent\n\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i]\n\n      if (typeof token === 'string') {\n        path += token\n\n        continue\n      }\n\n      var value = data[token.name]\n      var segment\n\n      if (value == null) {\n        if (token.optional) {\n          // Prepend partial segment prefixes.\n          if (token.partial) {\n            path += token.prefix\n          }\n\n          continue\n        } else {\n          throw new TypeError('Expected \"' + token.name + '\" to be defined')\n        }\n      }\n\n      if (isarray(value)) {\n        if (!token.repeat) {\n          throw new TypeError('Expected \"' + token.name + '\" to not repeat, but received `' + JSON.stringify(value) + '`')\n        }\n\n        if (value.length === 0) {\n          if (token.optional) {\n            continue\n          } else {\n            throw new TypeError('Expected \"' + token.name + '\" to not be empty')\n          }\n        }\n\n        for (var j = 0; j < value.length; j++) {\n          segment = encode(value[j])\n\n          if (!matches[i].test(segment)) {\n            throw new TypeError('Expected all \"' + token.name + '\" to match \"' + token.pattern + '\", but received `' + JSON.stringify(segment) + '`')\n          }\n\n          path += (j === 0 ? token.prefix : token.delimiter) + segment\n        }\n\n        continue\n      }\n\n      segment = token.asterisk ? encodeAsterisk(value) : encode(value)\n\n      if (!matches[i].test(segment)) {\n        throw new TypeError('Expected \"' + token.name + '\" to match \"' + token.pattern + '\", but received \"' + segment + '\"')\n      }\n\n      path += token.prefix + segment\n    }\n\n    return path\n  }\n}\n\n/**\n * Escape a regular expression string.\n *\n * @param  {string} str\n * @return {string}\n */\nfunction escapeString (str) {\n  return str.replace(/([.+*?=^!:${}()[\\]|\\/\\\\])/g, '\\\\$1')\n}\n\n/**\n * Escape the capturing group by escaping special characters and meaning.\n *\n * @param  {string} group\n * @return {string}\n */\nfunction escapeGroup (group) {\n  return group.replace(/([=!:$\\/()])/g, '\\\\$1')\n}\n\n/**\n * Attach the keys as a property of the regexp.\n *\n * @param  {!RegExp} re\n * @param  {Array}   keys\n * @return {!RegExp}\n */\nfunction attachKeys (re, keys) {\n  re.keys = keys\n  return re\n}\n\n/**\n * Get the flags for a regexp from the options.\n *\n * @param  {Object} options\n * @return {string}\n */\nfunction flags (options) {\n  return options.sensitive ? '' : 'i'\n}\n\n/**\n * Pull out keys from a regexp.\n *\n * @param  {!RegExp} path\n * @param  {!Array}  keys\n * @return {!RegExp}\n */\nfunction regexpToRegexp (path, keys) {\n  // Use a negative lookahead to match only capturing groups.\n  var groups = path.source.match(/\\((?!\\?)/g)\n\n  if (groups) {\n    for (var i = 0; i < groups.length; i++) {\n      keys.push({\n        name: i,\n        prefix: null,\n        delimiter: null,\n        optional: false,\n        repeat: false,\n        partial: false,\n        asterisk: false,\n        pattern: null\n      })\n    }\n  }\n\n  return attachKeys(path, keys)\n}\n\n/**\n * Transform an array into a regexp.\n *\n * @param  {!Array}  path\n * @param  {Array}   keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction arrayToRegexp (path, keys, options) {\n  var parts = []\n\n  for (var i = 0; i < path.length; i++) {\n    parts.push(pathToRegexp(path[i], keys, options).source)\n  }\n\n  var regexp = new RegExp('(?:' + parts.join('|') + ')', flags(options))\n\n  return attachKeys(regexp, keys)\n}\n\n/**\n * Create a path regexp from string input.\n *\n * @param  {string}  path\n * @param  {!Array}  keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction stringToRegexp (path, keys, options) {\n  return tokensToRegExp(parse(path, options), keys, options)\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n *\n * @param  {!Array}          tokens\n * @param  {(Array|Object)=} keys\n * @param  {Object=}         options\n * @return {!RegExp}\n */\nfunction tokensToRegExp (tokens, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */ (keys || options)\n    keys = []\n  }\n\n  options = options || {}\n\n  var strict = options.strict\n  var end = options.end !== false\n  var route = ''\n\n  // Iterate over the tokens and create our regexp string.\n  for (var i = 0; i < tokens.length; i++) {\n    var token = tokens[i]\n\n    if (typeof token === 'string') {\n      route += escapeString(token)\n    } else {\n      var prefix = escapeString(token.prefix)\n      var capture = '(?:' + token.pattern + ')'\n\n      keys.push(token)\n\n      if (token.repeat) {\n        capture += '(?:' + prefix + capture + ')*'\n      }\n\n      if (token.optional) {\n        if (!token.partial) {\n          capture = '(?:' + prefix + '(' + capture + '))?'\n        } else {\n          capture = prefix + '(' + capture + ')?'\n        }\n      } else {\n        capture = prefix + '(' + capture + ')'\n      }\n\n      route += capture\n    }\n  }\n\n  var delimiter = escapeString(options.delimiter || '/')\n  var endsWithDelimiter = route.slice(-delimiter.length) === delimiter\n\n  // In non-strict mode we allow a slash at the end of match. If the path to\n  // match already ends with a slash, we remove it for consistency. The slash\n  // is valid at the end of a path match, not in the middle. This is important\n  // in non-ending mode, where \"/test/\" shouldn't match \"/test//route\".\n  if (!strict) {\n    route = (endsWithDelimiter ? route.slice(0, -delimiter.length) : route) + '(?:' + delimiter + '(?=$))?'\n  }\n\n  if (end) {\n    route += '$'\n  } else {\n    // In non-ending mode, we need the capturing groups to match as much as\n    // possible by using a positive lookahead to the end or next path segment.\n    route += strict && endsWithDelimiter ? '' : '(?=' + delimiter + '|$)'\n  }\n\n  return attachKeys(new RegExp('^' + route, flags(options)), keys)\n}\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n *\n * @param  {(string|RegExp|Array)} path\n * @param  {(Array|Object)=}       keys\n * @param  {Object=}               options\n * @return {!RegExp}\n */\nfunction pathToRegexp (path, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */ (keys || options)\n    keys = []\n  }\n\n  options = options || {}\n\n  if (path instanceof RegExp) {\n    return regexpToRegexp(path, /** @type {!Array} */ (keys))\n  }\n\n  if (isarray(path)) {\n    return arrayToRegexp(/** @type {!Array} */ (path), /** @type {!Array} */ (keys), options)\n  }\n\n  return stringToRegexp(/** @type {string} */ (path), /** @type {!Array} */ (keys), options)\n}\n", "module.exports = Array.isArray || function (arr) {\n  return Object.prototype.toString.call(arr) == '[object Array]';\n};\n", "import pathToRegexp from \"path-to-regexp\";\n\nconst cache = {};\nconst cacheLimit = 10000;\nlet cacheCount = 0;\n\nfunction compilePath(path) {\n  if (cache[path]) return cache[path];\n\n  const generator = pathToRegexp.compile(path);\n\n  if (cacheCount < cacheLimit) {\n    cache[path] = generator;\n    cacheCount++;\n  }\n\n  return generator;\n}\n\n/**\n * Public API for generating a URL pathname from a path and parameters.\n */\nfunction generatePath(path = \"/\", params = {}) {\n  return path === \"/\" ? path : compilePath(path)(params, { pretty: true });\n}\n\nexport default generatePath;\n", "import pathToRegexp from \"path-to-regexp\";\n\nconst cache = {};\nconst cacheLimit = 10000;\nlet cacheCount = 0;\n\nfunction compilePath(path, options) {\n  const cacheKey = `${options.end}${options.strict}${options.sensitive}`;\n  const pathCache = cache[cacheKey] || (cache[cacheKey] = {});\n\n  if (pathCache[path]) return pathCache[path];\n\n  const keys = [];\n  const regexp = pathToRegexp(path, keys, options);\n  const result = { regexp, keys };\n\n  if (cacheCount < cacheLimit) {\n    pathCache[path] = result;\n    cacheCount++;\n  }\n\n  return result;\n}\n\n/**\n * Public API for matching a URL pathname to a path.\n */\nfunction matchPath(pathname, options = {}) {\n  if (typeof options === \"string\" || Array.isArray(options)) {\n    options = { path: options };\n  }\n\n  const { path, exact = false, strict = false, sensitive = false } = options;\n\n  const paths = [].concat(path);\n\n  return paths.reduce((matched, path) => {\n    if (!path && path !== \"\") return null;\n    if (matched) return matched;\n\n    const { regexp, keys } = compilePath(path, {\n      end: exact,\n      strict,\n      sensitive\n    });\n    const match = regexp.exec(pathname);\n\n    if (!match) return null;\n\n    const [url, ...values] = match;\n    const isExact = pathname === url;\n\n    if (exact && !isExact) return null;\n\n    return {\n      path, // the path used to match\n      url: path === \"/\" && url === \"\" ? \"/\" : url, // the matched portion of the URL\n      isExact, // whether or not we matched exactly\n      params: keys.reduce((memo, key, index) => {\n        memo[key.name] = values[index];\n        return memo;\n      }, {})\n    };\n  }, null);\n}\n\nexport default matchPath;\n", "import React from \"react\";\nimport { isValidElementType } from \"react-is\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport matchPath from \"./matchPath.js\";\n\nfunction isEmptyChildren(children) {\n  return React.Children.count(children) === 0;\n}\n\nfunction evalChildrenDev(children, props, path) {\n  const value = children(props);\n\n  warning(\n    value !== undefined,\n    \"You returned `undefined` from the `children` function of \" +\n      `<Route${path ? ` path=\"${path}\"` : \"\"}>, but you ` +\n      \"should have returned a React element or `null`\"\n  );\n\n  return value || null;\n}\n\n/**\n * The public API for matching a single path and rendering.\n */\nclass Route extends React.Component {\n  render() {\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(context, \"You should not use <Route> outside a <Router>\");\n\n          const location = this.props.location || context.location;\n          const match = this.props.computedMatch\n            ? this.props.computedMatch // <Switch> already computed the match for us\n            : this.props.path\n            ? matchPath(location.pathname, this.props)\n            : context.match;\n\n          const props = { ...context, location, match };\n\n          let { children, component, render } = this.props;\n\n          // Preact uses an empty array as children by\n          // default, so use null if that's the case.\n          if (Array.isArray(children) && isEmptyChildren(children)) {\n            children = null;\n          }\n\n          return (\n            <RouterContext.Provider value={props}>\n              {props.match\n                ? children\n                  ? typeof children === \"function\"\n                    ? __DEV__\n                      ? evalChildrenDev(children, props, this.props.path)\n                      : children(props)\n                    : children\n                  : component\n                  ? React.createElement(component, props)\n                  : render\n                  ? render(props)\n                  : null\n                : typeof children === \"function\"\n                ? __DEV__\n                  ? evalChildrenDev(children, props, this.props.path)\n                  : children(props)\n                : null}\n            </RouterContext.Provider>\n          );\n        }}\n      </RouterContext.Consumer>\n    );\n  }\n}\n\nif (__DEV__) {\n  Route.propTypes = {\n    children: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),\n    component: (props, propName) => {\n      if (props[propName] && !isValidElementType(props[propName])) {\n        return new Error(\n          `Invalid prop 'component' supplied to 'Route': the prop is not a valid React component`\n        );\n      }\n    },\n    exact: PropTypes.bool,\n    location: PropTypes.object,\n    path: PropTypes.oneOfType([\n      PropTypes.string,\n      PropTypes.arrayOf(PropTypes.string)\n    ]),\n    render: PropTypes.func,\n    sensitive: PropTypes.bool,\n    strict: PropTypes.bool\n  };\n\n  Route.prototype.componentDidMount = function() {\n    warning(\n      !(\n        this.props.children &&\n        !isEmptyChildren(this.props.children) &&\n        this.props.component\n      ),\n      \"You should not use <Route component> and <Route children> in the same route; <Route component> will be ignored\"\n    );\n\n    warning(\n      !(\n        this.props.children &&\n        !isEmptyChildren(this.props.children) &&\n        this.props.render\n      ),\n      \"You should not use <Route render> and <Route children> in the same route; <Route render> will be ignored\"\n    );\n\n    warning(\n      !(this.props.component && this.props.render),\n      \"You should not use <Route component> and <Route render> in the same route; <Route render> will be ignored\"\n    );\n  };\n\n  Route.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      !(this.props.location && !prevProps.location),\n      '<Route> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.'\n    );\n\n    warning(\n      !(!this.props.location && prevProps.location),\n      '<Route> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.'\n    );\n  };\n}\n\nexport default Route;\n", "export default function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createLocation, createPath } from \"history\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport Router from \"./Router.js\";\n\nfunction addLeadingSlash(path) {\n  return path.charAt(0) === \"/\" ? path : \"/\" + path;\n}\n\nfunction addBasename(basename, location) {\n  if (!basename) return location;\n\n  return {\n    ...location,\n    pathname: addLeadingSlash(basename) + location.pathname\n  };\n}\n\nfunction stripBasename(basename, location) {\n  if (!basename) return location;\n\n  const base = addLeadingSlash(basename);\n\n  if (location.pathname.indexOf(base) !== 0) return location;\n\n  return {\n    ...location,\n    pathname: location.pathname.substr(base.length)\n  };\n}\n\nfunction createURL(location) {\n  return typeof location === \"string\" ? location : createPath(location);\n}\n\nfunction staticHandler(methodName) {\n  return () => {\n    invariant(false, \"You cannot %s with <StaticRouter>\", methodName);\n  };\n}\n\nfunction noop() {}\n\n/**\n * The public top-level API for a \"static\" <Router>, so-called because it\n * can't actually change the current location. Instead, it just records\n * location changes in a context object. Useful mainly in testing and\n * server-rendering scenarios.\n */\nclass StaticRouter extends React.Component {\n  navigateTo(location, action) {\n    const { basename = \"\", context = {} } = this.props;\n    context.action = action;\n    context.location = addBasename(basename, createLocation(location));\n    context.url = createURL(context.location);\n  }\n\n  handlePush = location => this.navigateTo(location, \"PUSH\");\n  handleReplace = location => this.navigateTo(location, \"REPLACE\");\n  handleListen = () => noop;\n  handleBlock = () => noop;\n\n  render() {\n    const { basename = \"\", context = {}, location = \"/\", ...rest } = this.props;\n\n    const history = {\n      createHref: path => addLeadingSlash(basename + createURL(path)),\n      action: \"POP\",\n      location: stripBasename(basename, createLocation(location)),\n      push: this.handlePush,\n      replace: this.handleReplace,\n      go: staticHandler(\"go\"),\n      goBack: staticHandler(\"goBack\"),\n      goForward: staticHandler(\"goForward\"),\n      listen: this.handleListen,\n      block: this.handleBlock\n    };\n\n    return <Router {...rest} history={history} staticContext={context} />;\n  }\n}\n\nif (__DEV__) {\n  StaticRouter.propTypes = {\n    basename: PropTypes.string,\n    context: PropTypes.object,\n    location: PropTypes.oneOfType([PropTypes.string, PropTypes.object])\n  };\n\n  StaticRouter.prototype.componentDidMount = function() {\n    warning(\n      !this.props.history,\n      \"<StaticRouter> ignores the history prop. To use a custom history, \" +\n        \"use `import { Router }` instead of `import { StaticRouter as Router }`.\"\n    );\n  };\n}\n\nexport default StaticRouter;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport matchPath from \"./matchPath.js\";\n\n/**\n * The public API for rendering the first <Route> that matches.\n */\nclass Switch extends React.Component {\n  render() {\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(context, \"You should not use <Switch> outside a <Router>\");\n\n          const location = this.props.location || context.location;\n\n          let element, match;\n\n          // We use React.Children.forEach instead of React.Children.toArray().find()\n          // here because toArray adds keys to all child elements and we do not want\n          // to trigger an unmount/remount for two <Route>s that render the same\n          // component at different URLs.\n          React.Children.forEach(this.props.children, child => {\n            if (match == null && React.isValidElement(child)) {\n              element = child;\n\n              const path = child.props.path || child.props.from;\n\n              match = path\n                ? matchPath(location.pathname, { ...child.props, path })\n                : context.match;\n            }\n          });\n\n          return match\n            ? React.cloneElement(element, { location, computedMatch: match })\n            : null;\n        }}\n      </RouterContext.Consumer>\n    );\n  }\n}\n\nif (__DEV__) {\n  Switch.propTypes = {\n    children: PropTypes.node,\n    location: PropTypes.object\n  };\n\n  Switch.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      !(this.props.location && !prevProps.location),\n      '<Switch> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.'\n    );\n\n    warning(\n      !(!this.props.location && prevProps.location),\n      '<Switch> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.'\n    );\n  };\n}\n\nexport default Switch;\n", "'use strict';\n\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\nvar ReactIs = require('react-is');\nvar REACT_STATICS = {\n    childContextTypes: true,\n    contextType: true,\n    contextTypes: true,\n    defaultProps: true,\n    displayName: true,\n    getDefaultProps: true,\n    getDerivedStateFromError: true,\n    getDerivedStateFromProps: true,\n    mixins: true,\n    propTypes: true,\n    type: true\n};\n\nvar KNOWN_STATICS = {\n    name: true,\n    length: true,\n    prototype: true,\n    caller: true,\n    callee: true,\n    arguments: true,\n    arity: true\n};\n\nvar FORWARD_REF_STATICS = {\n    '$$typeof': true,\n    render: true,\n    defaultProps: true,\n    displayName: true,\n    propTypes: true\n};\n\nvar MEMO_STATICS = {\n    '$$typeof': true,\n    compare: true,\n    defaultProps: true,\n    displayName: true,\n    propTypes: true,\n    type: true\n};\n\nvar TYPE_STATICS = {};\nTYPE_STATICS[ReactIs.ForwardRef] = FORWARD_REF_STATICS;\n\nfunction getStatics(component) {\n    if (ReactIs.isMemo(component)) {\n        return MEMO_STATICS;\n    }\n    return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n}\n\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\n\nfunction hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n    if (typeof sourceComponent !== 'string') {\n        // don't hoist over string (html) components\n\n        if (objectPrototype) {\n            var inheritedComponent = getPrototypeOf(sourceComponent);\n            if (inheritedComponent && inheritedComponent !== objectPrototype) {\n                hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n            }\n        }\n\n        var keys = getOwnPropertyNames(sourceComponent);\n\n        if (getOwnPropertySymbols) {\n            keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n        }\n\n        var targetStatics = getStatics(targetComponent);\n        var sourceStatics = getStatics(sourceComponent);\n\n        for (var i = 0; i < keys.length; ++i) {\n            var key = keys[i];\n            if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n                var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n                try {\n                    // Avoid failures from read-only properties\n                    defineProperty(targetComponent, key, descriptor);\n                } catch (e) {}\n            }\n        }\n\n        return targetComponent;\n    }\n\n    return targetComponent;\n}\n\nmodule.exports = hoistNonReactStatics;\n", "import React from \"react\";\nimport invariant from \"tiny-invariant\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport HistoryContext from \"./HistoryContext.js\";\nimport matchPath from \"./matchPath.js\";\n\nconst useContext = React.useContext;\n\nexport function useHistory() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useHistory()\"\n    );\n  }\n\n  return useContext(HistoryContext);\n}\n\nexport function useLocation() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useLocation()\"\n    );\n  }\n\n  return useContext(RouterContext).location;\n}\n\nexport function useParams() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useParams()\"\n    );\n  }\n\n  const match = useContext(RouterContext).match;\n  return match ? match.params : {};\n}\n\nexport function useRouteMatch(path) {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useRouteMatch()\"\n    );\n  }\n\n  const location = useLocation();\n  const match = useContext(RouterContext).match;\n  return path ? matchPath(location.pathname, path) : match;\n}\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\n\nimport Lifecycle from \"./Lifecycle.js\";\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * The public API for prompting the user before navigating away from a screen.\n */\nfunction Prompt({ message, when = true }) {\n  return (\n    <RouterContext.Consumer>\n      {context => {\n        invariant(context, \"You should not use <Prompt> outside a <Router>\");\n\n        if (!when || context.staticContext) return null;\n\n        const method = context.history.block;\n\n        return (\n          <Lifecycle\n            onMount={self => {\n              self.release = method(message);\n            }}\n            onUpdate={(self, prevProps) => {\n              if (prevProps.message !== message) {\n                self.release();\n                self.release = method(message);\n              }\n            }}\n            onUnmount={self => {\n              self.release();\n            }}\n            message={message}\n          />\n        );\n      }}\n    </RouterContext.Consumer>\n  );\n}\n\nif (__DEV__) {\n  const messageType = PropTypes.oneOfType([PropTypes.func, PropTypes.string]);\n\n  Prompt.propTypes = {\n    when: PropTypes.bool,\n    message: messageType.isRequired\n  };\n}\n\nexport default Prompt;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createLocation, locationsAreEqual } from \"history\";\nimport invariant from \"tiny-invariant\";\n\nimport Lifecycle from \"./Lifecycle.js\";\nimport RouterContext from \"./RouterContext.js\";\nimport generatePath from \"./generatePath.js\";\n\n/**\n * The public API for navigating programmatically with a component.\n */\nfunction Redirect({ computedMatch, to, push = false }) {\n  return (\n    <RouterContext.Consumer>\n      {context => {\n        invariant(context, \"You should not use <Redirect> outside a <Router>\");\n\n        const { history, staticContext } = context;\n\n        const method = push ? history.push : history.replace;\n        const location = createLocation(\n          computedMatch\n            ? typeof to === \"string\"\n              ? generatePath(to, computedMatch.params)\n              : {\n                  ...to,\n                  pathname: generatePath(to.pathname, computedMatch.params)\n                }\n            : to\n        );\n\n        // When rendering in a static context,\n        // set the new location immediately.\n        if (staticContext) {\n          method(location);\n          return null;\n        }\n\n        return (\n          <Lifecycle\n            onMount={() => {\n              method(location);\n            }}\n            onUpdate={(self, prevProps) => {\n              const prevLocation = createLocation(prevProps.to);\n              if (\n                !locationsAreEqual(prevLocation, {\n                  ...location,\n                  key: prevLocation.key\n                })\n              ) {\n                method(location);\n              }\n            }}\n            to={to}\n          />\n        );\n      }}\n    </RouterContext.Consumer>\n  );\n}\n\nif (__DEV__) {\n  Redirect.propTypes = {\n    push: PropTypes.bool,\n    from: PropTypes.string,\n    to: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired\n  };\n}\n\nexport default Redirect;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport hoistStatics from \"hoist-non-react-statics\";\nimport invariant from \"tiny-invariant\";\n\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * A public higher-order component to access the imperative API\n */\nfunction withRouter(Component) {\n  const displayName = `withRouter(${Component.displayName || Component.name})`;\n  const C = props => {\n    const { wrappedComponentRef, ...remainingProps } = props;\n\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(\n            context,\n            `You should not use <${displayName} /> outside a <Router>`\n          );\n          return (\n            <Component\n              {...remainingProps}\n              {...context}\n              ref={wrappedComponentRef}\n            />\n          );\n        }}\n      </RouterContext.Consumer>\n    );\n  };\n\n  C.displayName = displayName;\n  C.WrappedComponent = Component;\n\n  if (__DEV__) {\n    C.propTypes = {\n      wrappedComponentRef: PropTypes.oneOfType([\n        PropTypes.string,\n        PropTypes.func,\n        PropTypes.object\n      ])\n    };\n  }\n\n  return hoistStatics(C, Component);\n}\n\nexport default withRouter;\n"], "names": ["_setPrototypeOf", "o", "p", "Object", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "prototype", "create", "constructor", "defineProperty", "exports", "value", "b", "Symbol", "for", "c", "d", "e", "f", "g", "h", "k", "l", "m", "n", "q", "r", "t", "v", "w", "x", "a", "u", "$$typeof", "type", "y", "module", "require$$0", "getOwnPropertySymbols", "hasOwnProperty", "propIsEnumerable", "propertyIsEnumerable", "assign", "test1", "String", "getOwnPropertyNames", "test2", "i", "fromCharCode", "map", "join", "test3", "split", "for<PERSON>ach", "letter", "keys", "err", "shouldUseNative", "Function", "call", "bind", "emptyFunction", "emptyFunctionWithReset", "resetWarningCache", "shim", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "secret", "Error", "name", "getShim", "ReactPropTypes", "array", "isRequired", "bool", "func", "number", "object", "string", "symbol", "any", "arrayOf", "element", "elementType", "instanceOf", "node", "objectOf", "oneOf", "oneOfType", "shape", "exact", "checkPropTypes", "PropTypes", "require$$2", "_extends", "target", "arguments", "length", "source", "key", "apply", "this", "isAbsolute", "pathname", "char<PERSON>t", "spliceOne", "list", "index", "pop", "_typeof", "iterator", "obj", "prefix", "invariant", "condition", "createPath", "search", "hash", "path", "createLocation", "state", "currentLocation", "hashIndex", "indexOf", "substr", "searchIndex", "parsePath", "undefined", "decodeURI", "URIError", "to", "from", "toParts", "fromParts", "isToAbs", "isFromAbs", "mustEndAbs", "concat", "hasTrailingSlash", "last", "up", "part", "unshift", "result", "resolvePathname", "locationsAreEqual", "valueEqual", "Array", "isArray", "every", "item", "aType", "aValue", "valueOf", "bValue", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "window", "document", "createElement", "clamp", "lowerBound", "upperBound", "Math", "min", "max", "createMemoryHistory", "_props", "getUserConfirmation", "_props$initialEntries", "initialEntries", "_props$initialIndex", "initialIndex", "_props$keyLength", "<PERSON><PERSON><PERSON><PERSON>", "transitionManager", "prompt", "listeners", "setPrompt", "nextPrompt", "confirmTransitionTo", "action", "callback", "appendListener", "fn", "isActive", "listener", "push", "filter", "notifyListeners", "_len", "args", "_key", "createTransitionManager", "setState", "nextState", "history", "entries", "create<PERSON><PERSON>", "random", "toString", "entry", "createHref", "go", "nextIndex", "ok", "nextEntries", "slice", "splice", "replace", "goBack", "goForward", "canGo", "block", "listen", "MAX_SIGNED_31_BIT_INT", "commonjsGlobal", "globalThis", "global", "createNamedContext", "context", "createContext", "displayName", "React", "defaultValue", "calculateChangedBits", "contextProp", "getUniqueId", "Provider", "emitter", "handlers", "on", "handler", "off", "get", "set", "newValue", "changedBits", "createEventEmitter", "_this", "getChildContext", "componentWillReceiveProps", "nextProps", "oldValue", "objectIs", "render", "children", "Component", "childContextTypes", "Consumer", "observedBits", "_this2", "getValue", "onUpdate", "componentDidMount", "componentWillUnmount", "<PERSON><PERSON><PERSON><PERSON>", "contextTypes", "historyContext", "Router", "_isMounted", "_pendingLocation", "staticContext", "unlisten", "computeRootMatch", "url", "params", "isExact", "RouterContext", "match", "HistoryContext", "MemoryRouter", "createHistory", "Lifecycle", "onMount", "componentDidUpdate", "prevProps", "onUnmount", "str", "options", "tokensToFunction", "parse", "arr", "pathToRegexp", "tokensToRegExp", "PATH_REGEXP", "RegExp", "res", "group", "tokens", "defaultDelimiter", "delimiter", "exec", "escaped", "offset", "next", "capture", "modifier", "asterisk", "partial", "repeat", "optional", "pattern", "escapeString", "encodeURIComponentPretty", "encodeURI", "charCodeAt", "toUpperCase", "matches", "opts", "data", "encode", "pretty", "encodeURIComponent", "token", "segment", "TypeError", "isarray", "JSON", "stringify", "j", "test", "attachKeys", "re", "flags", "sensitive", "strict", "end", "route", "endsWithDelimiter", "groups", "regexpToRegexp", "parts", "arrayToRegexp", "stringToRegexp", "cache", "cacheLimit", "cacheCount", "generatePath", "generator", "compile", "compilePath", "matchPath", "reduce", "matched", "cache<PERSON>ey", "pathCache", "regexp", "values", "memo", "Route", "computedMatch", "component", "Children", "count", "isEmptyChildren", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "addLeadingSlash", "createURL", "static<PERSON><PERSON><PERSON>", "noop", "StaticRouter", "handlePush", "navigateTo", "handleReplace", "handleListen", "handleBlock", "basename", "addBasename", "rest", "base", "stripBasename", "Switch", "child", "isValidElement", "cloneElement", "REACT_STATICS", "contextType", "defaultProps", "getDefaultProps", "getDerivedStateFromError", "getDerivedStateFromProps", "mixins", "propTypes", "KNOWN_STATICS", "caller", "callee", "arity", "MEMO_STATICS", "compare", "TYPE_STATICS", "getStatics", "ReactIs", "isMemo", "ForwardRef", "getOwnPropertyDescriptor", "getPrototypeOf", "objectPrototype", "hoistNonReactStatics", "targetComponent", "sourceComponent", "blacklist", "inheritedComponent", "targetStatics", "sourceStatics", "descriptor", "useContext", "useLocation", "message", "when", "method", "self", "release", "prevLocation", "C", "wrappedComponentRef", "remainingProps", "ref", "WrappedComponent", "hoistStatics"], "mappings": "2OAAe,SAASA,EAAgBC,EAAGC,GAMzC,OALAF,EAAkBG,OAAOC,gBAAkB,SAAyBH,EAAGC,GAErE,OADAD,EAAEI,UAAYH,EACPD,IAGcA,EAAGC,GCLb,SAASI,EAAeC,EAAUC,GAC/CD,EAASE,UAAYN,OAAOO,OAAOF,EAAWC,WAE9CL,EADAG,EAASE,UAAUE,YAAcJ,EACRC,uOCKdL,OAAOS,eAAeC,EAAQ,aAAa,CAACC,OAAM,IAC/D,IAAIC,EAAE,mBAAoBC,QAAQA,OAAOC,IAAIC,EAAEH,EAAEC,OAAOC,IAAI,iBAAiB,MAAME,EAAEJ,EAAEC,OAAOC,IAAI,gBAAgB,MAAMG,EAAEL,EAAEC,OAAOC,IAAI,kBAAkB,MAAMI,EAAEN,EAAEC,OAAOC,IAAI,qBAAqB,MAAMK,EAAEP,EAAEC,OAAOC,IAAI,kBAAkB,MAAMM,EAAER,EAAEC,OAAOC,IAAI,kBAAkB,MAAMO,EAAET,EAAEC,OAAOC,IAAI,iBAAiB,MAAMQ,EAAEV,EAAEC,OAAOC,IAAI,oBAAoB,MAAMS,EAAEX,EAAEC,OAAOC,IAAI,yBAAyB,MAAMU,EAAEZ,EAAEC,OAAOC,IAAI,qBAAqB,MAAMf,EAAEa,EAAEC,OAAOC,IAAI,kBAAkB,MAAMW,EAAEb,EAAEC,OAAOC,IAAI,uBACpf,MAAMY,EAAEd,EAAEC,OAAOC,IAAI,cAAc,MAAMa,EAAEf,EAAEC,OAAOC,IAAI,cAAc,MAAMc,EAAEhB,EAAEC,OAAOC,IAAI,qBAAqB,MAAMe,EAAEjB,EAAEC,OAAOC,IAAI,mBAAmB,MAAM,SAASgB,EAAEC,GAAG,GAAG,iBAAkBA,GAAG,OAAOA,EAAE,CAAC,IAAIC,EAAED,EAAEE,SAAS,OAAOD,GAAG,KAAKjB,EAAE,OAAOgB,EAAEA,EAAEG,MAAQ,KAAKZ,EAAE,KAAKC,EAAE,KAAKN,EAAE,KAAKE,EAAE,KAAKD,EAAE,KAAKnB,EAAE,OAAOgC,EAAE,QAAQ,OAAOA,EAAEA,GAAGA,EAAEE,UAAY,KAAKZ,EAAE,KAAKG,EAAE,KAAKJ,EAAE,OAAOW,EAAE,QAAQ,OAAOC,GAAG,KAAKL,EAAE,KAAKD,EAAE,KAAKV,EAAE,OAAOgB,IAAI,SAASG,EAAEJ,GAAG,OAAOD,EAAEC,KAAKR,EAAEb,SAAeoB,EAAEpB,YAAkBY,EACzeZ,iBAAuBa,EAAEb,kBAAwBW,EAAEX,kBAAwBU,EAAEV,UAAgBK,EAAEL,aAAmBc,EAAEd,WAAiBO,EAAEP,OAAaiB,EAAEjB,OAAagB,EAAEhB,SAAeM,EAAEN,WAAiBS,EAAET,aAAmBQ,EAAER,WAAiBX,EAC/OW,qBAA2B,SAASqB,GAAG,MAAM,iBAAkBA,GAAG,mBAAoBA,GAAGA,IAAId,GAAGc,IAAIR,GAAGQ,IAAIZ,GAAGY,IAAIb,GAAGa,IAAIhC,GAAGgC,IAAIN,GAAG,iBAAkBM,GAAG,OAAOA,IAAIA,EAAEE,WAAWN,GAAGI,EAAEE,WAAWP,GAAGK,EAAEE,WAAWb,GAAGW,EAAEE,WAAWZ,GAAGU,EAAEE,WAAWT,GAAGO,EAAEE,WAAWL,GAAGG,EAAEE,WAAWJ,IAAInB,cAAoB,SAASqB,GAAG,OAAOI,EAAEJ,IAAID,EAAEC,KAAKT,GAAGZ,mBAAyByB,EAAEzB,oBAA0B,SAASqB,GAAG,OAAOD,EAAEC,KAAKV,GAAGX,oBAA0B,SAASqB,GAAG,OAAOD,EAAEC,KAAKX,GACjdV,YAAkB,SAASqB,GAAG,MAAM,iBAAkBA,GAAG,OAAOA,GAAGA,EAAEE,WAAWlB,GAAGL,eAAqB,SAASqB,GAAG,OAAOD,EAAEC,KAAKP,GAAGd,aAAmB,SAASqB,GAAG,OAAOD,EAAEC,KAAKd,GAAGP,SAAe,SAASqB,GAAG,OAAOD,EAAEC,KAAKJ,GAAGjB,SAAe,SAASqB,GAAG,OAAOD,EAAEC,KAAKL,GAAGhB,WAAiB,SAASqB,GAAG,OAAOD,EAAEC,KAAKf,GAAGN,aAAmB,SAASqB,GAAG,OAAOD,EAAEC,KAAKZ,GAAGT,eAAqB,SAASqB,GAAG,OAAOD,EAAEC,KAAKb,GAAGR,aAAmB,SAASqB,GAAG,OAAOD,EAAEC,KAAKhC,gyBCXxcqC,UAAiBC,ICKfC,wBAAwBtC,OAAOsC,uBAC/BC,EAAiBvC,OAAOM,UAAUiC,eAClCC,EAAmBxC,OAAOM,UAAUmC,sBAUxC,WACC,IACC,IAAKzC,OAAO0C,OACX,OAAO,EAMR,IAAIC,EAAQ,IAAIC,OAAO,OAEvB,GADAD,EAAM,GAAK,KACkC,MAAzC3C,OAAO6C,oBAAoBF,GAAO,GACrC,OAAO,EAKR,IADA,IAAIG,EAAQ,GACHC,EAAI,EAAGA,EAAI,GAAIA,IACvBD,EAAM,IAAMF,OAAOI,aAAaD,IAAMA,EAKvC,GAAwB,eAHX/C,OAAO6C,oBAAoBC,GAAOG,IAAI,SAAUzB,GAC5D,OAAOsB,EAAMtB,KAEH0B,KAAK,IACf,OAAO,EAIR,IAAIC,EAAQ,GAIZ,MAHA,uBAAuBC,MAAM,IAAIC,QAAQ,SAAUC,GAClDH,EAAMG,GAAUA,IAGf,yBADEtD,OAAOuD,KAAKvD,OAAO0C,OAAO,GAAIS,IAAQD,KAAK,IAM9C,MAAOM,GAER,OAAO,GAIQC,IAAoBzD,OAAO0C,OCjDlCgB,SAASC,KAAKC,KAAK5D,OAAOM,UAAUiC,gBCJ9C,SAASsB,KACT,SAASC,KACTA,EAAuBC,kBAAoBF,EAE3C,oBCEEzB,UDFe,WACf,SAAS4B,EAAKC,EAAOC,EAAUC,EAAeC,EAAUC,EAAcC,GACpE,GERuB,iDFQnBA,EAAJ,CAIA,IAAId,EAAM,IAAIe,MACZ,mLAKF,MADAf,EAAIgB,KAAO,sBACLhB,GAGR,SAASiB,IACP,OAAOT,EAIT,IAAIU,EAAiB,CACnBC,MAPFX,EAAKY,WAAaZ,EAQhBa,KAAMb,EACNc,KAAMd,EACNe,OAAQf,EACRgB,OAAQhB,EACRiB,OAAQjB,EACRkB,OAAQlB,EAERmB,IAAKnB,EACLoB,QAASX,EACTY,QAASrB,EACTsB,YAAatB,EACbuB,WAAYd,EACZe,KAAMxB,EACNyB,SAAUhB,EACViB,MAAOjB,EACPkB,UAAWlB,EACXmB,MAAOnB,EACPoB,MAAOpB,EAEPqB,eAAgBhC,EAChBC,kBAAmBF,GAKrB,OAFAa,EAAeqB,UAAYrB,EC3CVsB,KEjBJ,SAASC,IAetB,OAdAA,EAAWjG,OAAO0C,QAAU,SAAUwD,GACpC,IAAK,IAAInD,EAAI,EAAGA,EAAIoD,UAAUC,OAAQrD,IAAK,CACzC,IAAIsD,EAASF,UAAUpD,GAEvB,IAAK,IAAIuD,KAAOD,EACVrG,OAAOM,UAAUiC,eAAeoB,KAAK0C,EAAQC,KAC/CJ,EAAOI,GAAOD,EAAOC,IAK3B,OAAOJ,IAGOK,MAAMC,KAAML,WCf9B,SAASM,EAAWC,GAClB,MAA8B,MAAvBA,EAASC,OAAO,GAIzB,SAASC,EAAUC,EAAMC,GACvB,IAAK,IAAI/D,EAAI+D,EAAOzF,EAAI0B,EAAI,EAAGvB,EAAIqF,EAAKT,OAAQ/E,EAAIG,EAAGuB,GAAK,EAAG1B,GAAK,EAClEwF,EAAK9D,GAAK8D,EAAKxF,GAGjBwF,EAAKE,MCVP,IAAIC,EAA4B,mBAAXnG,QAAoD,iBAApBA,OAAOoG,SAAwB,SAAUC,GAAO,cAAcA,GAAS,SAAUA,GAAO,OAAOA,GAAyB,mBAAXrG,QAAyBqG,EAAI1G,cAAgBK,QAAUqG,IAAQrG,OAAOP,UAAY,gBAAkB4G,GCCtQ,IAAIC,EAAS,mBACb,SAASC,EAAUC,GACjB,IAAIA,EAKF,MAAM,IAAI9C,MAAM4C,GCqCpB,SAASG,EAAWlD,GAClB,IAAIsC,EAAWtC,EAASsC,SACpBa,EAASnD,EAASmD,OAClBC,EAAOpD,EAASoD,KAChBC,EAAOf,GAAY,IAGvB,OAFIa,GAAqB,MAAXA,IAAgBE,GAA6B,MAArBF,EAAOZ,OAAO,GAAaY,EAAS,IAAMA,GAC5EC,GAAiB,MAATA,IAAcC,GAA2B,MAAnBD,EAAKb,OAAO,GAAaa,EAAO,IAAMA,GACjEC,EAGT,SAASC,EAAeD,EAAME,EAAOrB,EAAKsB,GACxC,IAAIxD,EAEgB,iBAATqD,GAETrD,EAvCJ,SAAmBqD,GACjB,IAAIf,EAAWe,GAAQ,IACnBF,EAAS,GACTC,EAAO,GACPK,EAAYnB,EAASoB,QAAQ,MAEd,IAAfD,IACFL,EAAOd,EAASqB,OAAOF,GACvBnB,EAAWA,EAASqB,OAAO,EAAGF,IAGhC,IAAIG,EAActB,EAASoB,QAAQ,KAOnC,OALqB,IAAjBE,IACFT,EAASb,EAASqB,OAAOC,GACzBtB,EAAWA,EAASqB,OAAO,EAAGC,IAGzB,CACLtB,SAAUA,EACVa,OAAmB,MAAXA,EAAiB,GAAKA,EAC9BC,KAAe,MAATA,EAAe,GAAKA,GAkBfS,CAAUR,IACZE,MAAQA,QAISO,KAD1B9D,EAAW6B,EAAS,GAAIwB,IACXf,WAAwBtC,EAASsC,SAAW,IAErDtC,EAASmD,OACuB,MAA9BnD,EAASmD,OAAOZ,OAAO,KAAYvC,EAASmD,OAAS,IAAMnD,EAASmD,QAExEnD,EAASmD,OAAS,GAGhBnD,EAASoD,KACqB,MAA5BpD,EAASoD,KAAKb,OAAO,KAAYvC,EAASoD,KAAO,IAAMpD,EAASoD,MAEpEpD,EAASoD,KAAO,QAGJU,IAAVP,QAA0CO,IAAnB9D,EAASuD,QAAqBvD,EAASuD,MAAQA,IAG5E,IACEvD,EAASsC,SAAWyB,UAAU/D,EAASsC,UACvC,MAAOzF,GACP,MAAIA,aAAamH,SACT,IAAIA,SAAS,aAAehE,EAASsC,SAAW,iFAEhDzF,EAoBV,OAhBIqF,IAAKlC,EAASkC,IAAMA,GAEpBsB,EAEGxD,EAASsC,SAE6B,MAAhCtC,EAASsC,SAASC,OAAO,KAClCvC,EAASsC,SHrFf,SAAyB2B,EAAzB,GACE,IAAIC,EAA0B,EAAnBnC,UAAUC,aAA+B8B,IADtD,EAAA,EACiF,GAE3EK,EAAUF,GAAMA,EAAGjF,MAAM,MAAQ,GACjCoF,EAAYF,GAAQA,EAAKlF,MAAM,MAAQ,GAEvCqF,EAAUJ,GAAM5B,EAAW4B,GAC3BK,EAAYJ,GAAQ7B,EAAW6B,GAC/BK,EAAaF,GAAWC,EAW5B,GATIL,GAAM5B,EAAW4B,GAEnBG,EAAYD,EACHA,EAAQnC,SAEjBoC,EAAUzB,MACVyB,EAAYA,EAAUI,OAAOL,KAG1BC,EAAUpC,OAAQ,MAAO,IAE9B,IAAIyC,OAAmB,EACvB,GAAIL,EAAUpC,OAAQ,CACpB,IAAI0C,EAAON,EAAUA,EAAUpC,OAAS,GACxCyC,EAA4B,MAATC,GAAyB,OAATA,GAA0B,KAATA,OAEpDD,GAAmB,EAIrB,IADA,IAAIE,EAAK,EACAhG,EAAIyF,EAAUpC,OAAa,GAALrD,EAAQA,IAAK,CAC1C,IAAIiG,EAAOR,EAAUzF,GAER,MAATiG,EACFpC,EAAU4B,EAAWzF,GACH,OAATiG,GACTpC,EAAU4B,EAAWzF,GACrBgG,KACSA,IACTnC,EAAU4B,EAAWzF,GACrBgG,KAIJ,IAAKJ,EAAY,KAAOI,KACtBP,EAAUS,QAAQ,OACfN,GAA+B,KAAjBH,EAAU,IAAeA,EAAU,IAAO/B,EAAW+B,EAAU,KAAMA,EAAUS,QAAQ,IAE1G,IAAIC,EAASV,EAAUtF,KAAK,KAI5B,OAFI2F,GAA0C,MAAtBK,EAAOnB,QAAQ,KAAYmB,GAAU,KAEtDA,EGiCiBC,CAAgB/E,EAASsC,SAAUkB,EAAgBlB,WAFvEtC,EAASsC,SAAWkB,EAAgBlB,SAMjCtC,EAASsC,WACZtC,EAASsC,SAAW,KAIjBtC,EAET,SAASgF,EAAkBrH,EAAGnB,GAC5B,OAAOmB,EAAE2E,WAAa9F,EAAE8F,UAAY3E,EAAEwF,SAAW3G,EAAE2G,QAAUxF,EAAEyF,OAAS5G,EAAE4G,MAAQzF,EAAEuE,MAAQ1F,EAAE0F,KF7GhG,SAAS+C,EAAWtH,EAAGnB,GACrB,GAAImB,IAAMnB,EAAG,OAAO,EAEpB,GAAS,MAALmB,GAAkB,MAALnB,EAAW,OAAO,EAEnC,GAAI0I,MAAMC,QAAQxH,GAChB,OAAOuH,MAAMC,QAAQ3I,IAAMmB,EAAEqE,SAAWxF,EAAEwF,QAAUrE,EAAEyH,MAAM,SAAUC,EAAM3C,GAC1E,OAAOuC,EAAWI,EAAM7I,EAAEkG,MAI9B,IAAI4C,OAAqB,IAAN3H,EAAoB,YAAciF,EAAQjF,GAG7D,GAAI2H,UAFqB,IAAN9I,EAAoB,YAAcoG,EAAQpG,IAExC,OAAO,EAE5B,GAAc,WAAV8I,EAgBJ,OAAO,EAfL,IAAIC,EAAS5H,EAAE6H,UACXC,EAASjJ,EAAEgJ,UAEf,GAAID,IAAW5H,GAAK8H,IAAWjJ,EAAG,OAAOyI,EAAWM,EAAQE,GAE5D,IAAIC,EAAQ9J,OAAOuD,KAAKxB,GACpBgI,EAAQ/J,OAAOuD,KAAK3C,GAExB,OAAIkJ,EAAM1D,SAAW2D,EAAM3D,QAEpB0D,EAAMN,MAAM,SAAUlD,GAC3B,OAAO+C,EAAWtH,EAAEuE,GAAM1F,EAAE0F,MEiFqE+C,CAAWtH,EAAE4F,MAAO/G,EAAE+G,OAyExF,oBAAXqC,QAA0BA,OAAOC,UAAYD,OAAOC,SAASC,cAgkBvF,SAASC,EAAM3I,EAAG4I,EAAYC,GAC5B,OAAOC,KAAKC,IAAID,KAAKE,IAAIhJ,EAAG4I,GAAaC,GAO3C,SAASI,EAAoBxG,QACb,IAAVA,IACFA,EAAQ,IAGV,IAAIyG,EAASzG,EACT0G,EAAsBD,EAAOC,oBAC7BC,EAAwBF,EAAOG,eAC/BA,OAA2C,IAA1BD,EAAmC,CAAC,KAAOA,EAC5DE,EAAsBJ,EAAOK,aAC7BA,OAAuC,IAAxBD,EAAiC,EAAIA,EACpDE,EAAmBN,EAAOO,UAC1BA,OAAiC,IAArBD,EAA8B,EAAIA,EAC9CE,EA3pBN,WACE,IAAIC,EAAS,KAiCTC,EAAY,GA4BhB,MAAO,CACLC,UA5DF,SAAmBC,GAGjB,OADAH,EAASG,EACF,WACDH,IAAWG,IAAYH,EAAS,QAyDtCI,oBArDF,SAA6BnH,EAAUoH,EAAQb,EAAqBc,GAIlE,GAAc,MAAVN,EAAgB,CAClB,IAAIjC,EAA2B,mBAAXiC,EAAwBA,EAAO/G,EAAUoH,GAAUL,EAEjD,iBAAXjC,EAC0B,mBAAxByB,EACTA,EAAoBzB,EAAQuC,GAG5BA,GAAS,GAIXA,GAAoB,IAAXvC,QAGXuC,GAAS,IAmCXC,eA7BF,SAAwBC,GACtB,IAAIC,GAAW,EAEf,SAASC,IACHD,GAAUD,EAAGpF,WAAM,EAAQJ,WAIjC,OADAiF,EAAUU,KAAKD,GACR,WACLD,GAAW,EACXR,EAAYA,EAAUW,OAAO,SAAUtC,GACrC,OAAOA,IAASoC,MAmBpBG,gBAdF,WACE,IAAK,IAAIC,EAAO9F,UAAUC,OAAQ8F,EAAO,IAAI5C,MAAM2C,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,GAAQhG,UAAUgG,GAGzBf,EAAU/H,QAAQ,SAAUwI,GAC1B,OAAOA,EAAStF,WAAM,EAAQ2F,OAimBVE,GAExB,SAASC,EAASC,GAChBrG,EAASsG,EAASD,GAElBC,EAAQnG,OAASmG,EAAQC,QAAQpG,OACjC8E,EAAkBc,gBAAgBO,EAAQnI,SAAUmI,EAAQf,QAG9D,SAASiB,IACP,OAAOnC,KAAKoC,SAASC,SAAS,IAAI5E,OAAO,EAAGkD,GAG9C,IAAInE,EAAQqD,EAAMY,EAAc,EAAGF,EAAezE,OAAS,GACvDoG,EAAU3B,EAAe5H,IAAI,SAAU2J,GACzC,OAAmClF,EAAekF,OAAO1E,EAAjC,iBAAV0E,EAAsDH,IAAgDG,EAAMtG,KAAOmG,OAG/HI,EAAavF,EAyCjB,SAASwF,EAAGtL,GACV,IAAIuL,EAAY5C,EAAMoC,EAAQzF,MAAQtF,EAAG,EAAG+K,EAAQC,QAAQpG,OAAS,GAEjEhC,EAAWmI,EAAQC,QAAQO,GAC/B7B,EAAkBK,oBAAoBnH,EAFzB,MAE2CuG,EAAqB,SAAUqC,GACjFA,EACFX,EAAS,CACPb,OALO,MAMPpH,SAAUA,EACV0C,MAAOiG,IAKTV,MA8BN,IAAIE,EAAU,CACZnG,OAAQoG,EAAQpG,OAChBoF,OAAQ,MACRpH,SAAUoI,EAAQ1F,GAClBA,MAAOA,EACP0F,QAASA,EACTK,WAAYA,EACZf,KA1FF,SAAcrE,EAAME,GAElB,IACIvD,EAAWsD,EAAeD,EAAME,EAAO8E,IAAaF,EAAQnI,UAChE8G,EAAkBK,oBAAoBnH,EAFzB,OAE2CuG,EAAqB,SAAUqC,GACrF,GAAKA,EAAL,CACA,IACID,EADYR,EAAQzF,MACI,EACxBmG,EAAcV,EAAQC,QAAQU,MAAM,GAEpCD,EAAY7G,OAAS2G,EACvBE,EAAYE,OAAOJ,EAAWE,EAAY7G,OAAS2G,EAAW3I,GAE9D6I,EAAYnB,KAAK1H,GAGnBiI,EAAS,CACPb,OAfS,OAgBTpH,SAAUA,EACV0C,MAAOiG,EACPP,QAASS,QAuEbG,QAlEF,SAAiB3F,EAAME,GAErB,IAAI6D,EAAS,UACTpH,EAAWsD,EAAeD,EAAME,EAAO8E,IAAaF,EAAQnI,UAChE8G,EAAkBK,oBAAoBnH,EAAUoH,EAAQb,EAAqB,SAAUqC,GAChFA,IACLT,EAAQC,QAAQD,EAAQzF,OAAS1C,EACjCiI,EAAS,CACPb,OAAQA,EACRpH,SAAUA,QA0Dd0I,GAAIA,EACJO,OAnCF,WACEP,GAAI,IAmCJQ,UAhCF,WACER,EAAG,IAgCHS,MA7BF,SAAe/L,GACb,IAAIuL,EAAYR,EAAQzF,MAAQtF,EAChC,OAAoB,GAAbuL,GAAkBA,EAAYR,EAAQC,QAAQpG,QA4BrDoH,MAzBF,SAAerC,GAKb,YAJe,IAAXA,IACFA,GAAS,GAGJD,EAAkBG,UAAUF,IAqBnCsC,OAlBF,SAAgB5B,GACd,OAAOX,EAAkBQ,eAAeG,KAmB1C,OAAOU,EC53BT,IAAMmB,EAAwB,WAExBC,EACkB,oBAAfC,WAEHA,WACkB,oBAAX5D,OACPA,OACkB,oBAAX6D,OACPA,OACA,GCfqB,SAArBC,EAAqBtJ,OACnBuJ,EAAUC,WAChBD,EAAQE,YAAczJ,EAEfuJ,QCAMG,EAAMF,eFuDN,SAA4BG,EAAcC,WACjDC,EAAc,0BA3CtB,eACM/H,EAAM,8BACFqH,EAAerH,IAAQqH,EAAerH,IAAQ,GAAK,EAyCXgI,GAAgB,KAE1DC,sJACJC,QA9BJ,SAA4B7N,OACtB8N,EAAW,SACR,CACLC,YAAGC,GACDF,EAAS3C,KAAK6C,IAGhBC,aAAID,GACFF,EAAWA,EAAS1C,OAAO,SAAA3K,UAAKA,IAAMuN,KAGxCE,sBACSlO,GAGTmO,aAAIC,EAAUC,GACZrO,EAAQoO,EACRN,EAASpL,QAAQ,SAAAsL,UAAWA,EAAQhO,EAAOqO,OAanCC,CAAmBC,EAAKjL,MAAMtD,2CAMxCwO,gBAAA,8BAEKd,GAAc7H,KAAKgI,aAIxBY,0BAAA,SAA0BC,MACpB7I,KAAKvC,MAAMtD,QAAU0O,EAAU1O,MAAO,KAGpCqO,EAFAM,EAAW9I,KAAKvC,MAAMtD,MACtBoO,EAAWM,EAAU1O,OAtDjC,SAAkBmB,EAAGK,UACfL,IAAMK,EACK,IAANL,GAAW,EAAIA,GAAM,EAAIK,EAGzBL,GAAMA,GAAKK,GAAMA,EAoDhBoN,CAASD,EAAUP,IAGrBC,EACkC,mBAAzBZ,EACHA,EAAqBkB,EAAUP,GAC/BrB,EAYc,KAFpBsB,GAAe,SAGRR,QAAQM,IAAIO,EAAU1O,MAAOqO,IAlBpCA,EAAc,MAwBpBQ,OAAA,kBACShJ,KAAKvC,MAAMwL,aA7CCvB,EAAMwB,WAAvBnB,EAGGoB,0BACJtB,GAActI,EAAUf,OAAOJ,kBA6C9BgL,sJAKJC,sBAEAlI,MAAQ,CACNhH,MAAOmP,EAAKC,cAoCdC,SAAW,SAACjB,EAAUC,GAEiB,KADI,EAApBc,EAAKD,cACNb,MACb3C,SAAS,CAAE1L,MAAOmP,EAAKC,kDApChCX,0BAAA,SAA0BC,OAClBQ,EAAiBR,EAAjBQ,kBACDA,aACHA,MAAAA,EACInC,EACAmC,KAGRI,kBAAA,WACMzJ,KAAKuH,QAAQM,SACVN,QAAQM,GAAaK,GAAGlI,KAAKwJ,cAE9BH,EAAiBrJ,KAAKvC,MAAtB4L,kBACDA,aACHA,MAAAA,EACInC,EACAmC,KAGRK,qBAAA,WACM1J,KAAKuH,QAAQM,SACVN,QAAQM,GAAaO,IAAIpI,KAAKwJ,aAIvCD,SAAA,kBACMvJ,KAAKuH,QAAQM,GACR7H,KAAKuH,QAAQM,GAAaQ,MAE1BV,KAWXqB,OAAA,kBA3GJ,SAAmBC,UACVnG,MAAMC,QAAQkG,GAAYA,EAAS,GAAKA,EA2GpCU,CAAU3J,KAAKvC,MAAMwL,SAArBU,CAA+B3J,KAAKmB,MAAMhH,WApD9BuN,EAAMwB,kBAAvBE,EACGQ,qBACJ/B,GAActI,EAAUf,UAsDtB,CACLuJ,SAAAA,EACAqB,SAAAA,IG1KES,EAA+BvC,EAAmB,kBCAlDC,EAAwBD,EAAmB,UCQ3CwC,yBAKQrM,8BACJA,UAED0D,MAAQ,CACXvD,SAAUH,EAAMsI,QAAQnI,YAQrBmM,YAAa,IACbC,iBAAmB,KAEnBvM,EAAMwM,kBACJC,SAAWzM,EAAMsI,QAAQkB,OAAO,SAAArJ,KAC9BoM,iBAAmBpM,gBArBvBuM,iBAAP,SAAwBjK,SACf,CAAEe,KAAM,IAAKmJ,IAAK,IAAKC,OAAQ,GAAIC,QAAsB,MAAbpK,+BAyBrDuJ,kBAAA,2BACOM,YAAa,EAEd/J,KAAKkK,eAGFA,WAEFlK,KAAKvC,MAAMwM,qBACTC,SAAWlK,KAAKvC,MAAMsI,QAAQkB,OAAO,SAAArJ,GACpC0L,EAAKS,YACPT,EAAKzD,SAAS,CAAEjI,SAAAA,OAIlBoC,KAAKgK,uBACFnE,SAAS,CAAEjI,SAAUoC,KAAKgK,sBAInCN,qBAAA,WACM1J,KAAKkK,gBACFA,gBACAH,YAAa,OACbC,iBAAmB,SAI5BhB,OAAA,kBAEItB,gBAAC6C,EAAcxC,UACb5N,MAAO,CACL4L,QAAS/F,KAAKvC,MAAMsI,QACpBnI,SAAUoC,KAAKmB,MAAMvD,SACrB4M,MAAOV,EAAOK,iBAAiBnK,KAAKmB,MAAMvD,SAASsC,UACnD+J,cAAejK,KAAKvC,MAAMwM,gBAG5BvC,gBAAC+C,EAAe1C,UACdkB,SAAUjJ,KAAKvC,MAAMwL,UAAY,KACjC9O,MAAO6F,KAAKvC,MAAMsI,eAnEP2B,EAAMwB,WCArBwB,sJACJ3E,QAAU4E,EAAcjC,EAAKjL,mCAE7BuL,OAAA,kBACStB,gBAACoC,GAAO/D,QAAS/F,KAAK+F,QAASkD,SAAUjJ,KAAKvC,MAAMwL,eAJpCvB,EAAMwB,WCR3B0B,kGACJnB,kBAAA,WACMzJ,KAAKvC,MAAMoN,SAAS7K,KAAKvC,MAAMoN,QAAQ1N,KAAK6C,KAAMA,SAGxD8K,mBAAA,SAAmBC,GACb/K,KAAKvC,MAAM+L,UAAUxJ,KAAKvC,MAAM+L,SAASrM,KAAK6C,KAAMA,KAAM+K,MAGhErB,qBAAA,WACM1J,KAAKvC,MAAMuN,WAAWhL,KAAKvC,MAAMuN,UAAU7N,KAAK6C,KAAMA,SAG5DgJ,OAAA,kBACS,SAdatB,EAAMwB,WC2G9B,WAAkB+B,EAAKC,GACrB,OAAOC,EAAiBC,EAAMH,EAAKC,IC9GrC,MAAiBpI,MAAMC,SAAW,SAAUsI,GAC1C,MAA8C,kBAAvC7R,OAAOM,UAAUqM,SAAShJ,KAAKkO,MDIvBC,IACMF,IAEWD,IACFI,EAO5BC,EAAc,IAAIC,OAAO,CAG3B,UAOA,0GACA/O,KAAK,KAAM,KASb,SAAS0O,EAAOH,EAAKC,GAQnB,IAPA,IAKIQ,EAmMgBC,EAxMhBC,EAAS,GACT9L,EAAM,EACNQ,EAAQ,EACRW,EAAO,GACP4K,EAAmBX,GAAWA,EAAQY,WAAa,IAGf,OAAhCJ,EAAMF,EAAYO,KAAKd,KAAe,CAC5C,IAAIlQ,EAAI2Q,EAAI,GACRM,EAAUN,EAAI,GACdO,EAASP,EAAIpL,MAKjB,GAJAW,GAAQgK,EAAIvE,MAAMpG,EAAO2L,GACzB3L,EAAQ2L,EAASlR,EAAE6E,OAGfoM,EACF/K,GAAQ+K,EAAQ,OADlB,CAKA,IAAIE,EAAOjB,EAAI3K,GACXK,EAAS+K,EAAI,GACb1N,EAAO0N,EAAI,GACXS,EAAUT,EAAI,GACdC,EAAQD,EAAI,GACZU,EAAWV,EAAI,GACfW,EAAWX,EAAI,GAGfzK,IACF2K,EAAOtG,KAAKrE,GACZA,EAAO,IAGT,IAAIqL,EAAoB,MAAV3L,GAA0B,MAARuL,GAAgBA,IAASvL,EACrD4L,EAAsB,MAAbH,GAAiC,MAAbA,EAC7BI,EAAwB,MAAbJ,GAAiC,MAAbA,EAC/BN,EAAYJ,EAAI,IAAMG,EACtBY,EAAUN,GAAWR,EAEzBC,EAAOtG,KAAK,CACVtH,KAAMA,GAAQ8B,IACda,OAAQA,GAAU,GAClBmL,UAAWA,EACXU,SAAUA,EACVD,OAAQA,EACRD,QAASA,EACTD,WAAYA,EACZI,QAASA,GAwJOd,EAxJec,EAyJ5Bd,EAAM/E,QAAQ,gBAAiB,SAzJSyF,EAAW,KAAO,KAAOK,EAAaZ,GAAa,SAclG,OATIxL,EAAQ2K,EAAIrL,SACdqB,GAAQgK,EAAI1J,OAAOjB,IAIjBW,GACF2K,EAAOtG,KAAKrE,GAGP2K,EAoBT,SAASe,EAA0B1B,GACjC,OAAO2B,UAAU3B,GAAKrE,QAAQ,UAAW,SAAUrM,GACjD,MAAO,IAAMA,EAAEsS,WAAW,GAAG1G,SAAS,IAAI2G,gBAmB9C,SAAS3B,EAAkBS,GAKzB,IAHA,IAAImB,EAAU,IAAIjK,MAAM8I,EAAOhM,QAGtBrD,EAAI,EAAGA,EAAIqP,EAAOhM,OAAQrD,IACR,iBAAdqP,EAAOrP,KAChBwQ,EAAQxQ,GAAK,IAAIkP,OAAO,OAASG,EAAOrP,GAAGkQ,QAAU,OAIzD,OAAO,SAAU/L,EAAKsM,GAMpB,IALA,IAAI/L,EAAO,GACPgM,EAAOvM,GAAO,GAEdwM,GADUF,GAAQ,IACDG,OAASR,EAA2BS,mBAEhD7Q,EAAI,EAAGA,EAAIqP,EAAOhM,OAAQrD,IAAK,CACtC,IAAI8Q,EAAQzB,EAAOrP,GAEnB,GAAqB,iBAAV8Q,EAAX,CAMA,IACIC,EADAnT,EAAQ8S,EAAKI,EAAMrP,MAGvB,GAAa,MAAT7D,EAAe,CACjB,GAAIkT,EAAMb,SAAU,CAEda,EAAMf,UACRrL,GAAQoM,EAAM1M,QAGhB,SAEA,MAAM,IAAI4M,UAAU,aAAeF,EAAMrP,KAAO,mBAIpD,GAAIwP,EAAQrT,GAAZ,CACE,IAAKkT,EAAMd,OACT,MAAM,IAAIgB,UAAU,aAAeF,EAAMrP,KAAO,kCAAoCyP,KAAKC,UAAUvT,GAAS,KAG9G,GAAqB,IAAjBA,EAAMyF,OAAc,CACtB,GAAIyN,EAAMb,SACR,SAEA,MAAM,IAAIe,UAAU,aAAeF,EAAMrP,KAAO,qBAIpD,IAAK,IAAI2P,EAAI,EAAGA,EAAIxT,EAAMyF,OAAQ+N,IAAK,CAGrC,GAFAL,EAAUJ,EAAO/S,EAAMwT,KAElBZ,EAAQxQ,GAAGqR,KAAKN,GACnB,MAAM,IAAIC,UAAU,iBAAmBF,EAAMrP,KAAO,eAAiBqP,EAAMZ,QAAU,oBAAsBgB,KAAKC,UAAUJ,GAAW,KAGvIrM,IAAe,IAAN0M,EAAUN,EAAM1M,OAAS0M,EAAMvB,WAAawB,OApBzD,CA4BA,GAFAA,EAAUD,EAAMhB,SA5EbO,UA4EuCzS,GA5ExByM,QAAQ,QAAS,SAAUrM,GAC/C,MAAO,IAAMA,EAAEsS,WAAW,GAAG1G,SAAS,IAAI2G,gBA2EWI,EAAO/S,IAErD4S,EAAQxQ,GAAGqR,KAAKN,GACnB,MAAM,IAAIC,UAAU,aAAeF,EAAMrP,KAAO,eAAiBqP,EAAMZ,QAAU,oBAAsBa,EAAU,KAGnHrM,GAAQoM,EAAM1M,OAAS2M,QArDrBrM,GAAQoM,EAwDZ,OAAOpM,GAUX,SAASyL,EAAczB,GACrB,OAAOA,EAAIrE,QAAQ,6BAA8B,QAoBnD,SAASiH,EAAYC,EAAI/Q,GAEvB,OADA+Q,EAAG/Q,KAAOA,EACH+Q,EAST,SAASC,EAAO7C,GACd,OAAOA,EAAQ8C,UAAY,GAAK,IAwElC,SAASzC,EAAgBK,EAAQ7O,EAAMmO,GAChCsC,EAAQzQ,KACXmO,EAAkCnO,GAAQmO,EAC1CnO,EAAO,IAUT,IALA,IAAIkR,GAFJ/C,EAAUA,GAAW,IAEA+C,OACjBC,GAAsB,IAAhBhD,EAAQgD,IACdC,EAAQ,GAGH5R,EAAI,EAAGA,EAAIqP,EAAOhM,OAAQrD,IAAK,CACtC,IAAI8Q,EAAQzB,EAAOrP,GAEnB,GAAqB,iBAAV8Q,EACTc,GAASzB,EAAaW,OACjB,CACL,IAAI1M,EAAS+L,EAAaW,EAAM1M,QAC5BwL,EAAU,MAAQkB,EAAMZ,QAAU,IAEtC1P,EAAKuI,KAAK+H,GAENA,EAAMd,SACRJ,GAAW,MAAQxL,EAASwL,EAAU,MAaxCgC,GANIhC,EAJAkB,EAAMb,SACHa,EAAMf,QAGC3L,EAAS,IAAMwL,EAAU,KAFzB,MAAQxL,EAAS,IAAMwL,EAAU,MAKnCxL,EAAS,IAAMwL,EAAU,KAOzC,IAAIL,EAAYY,EAAaxB,EAAQY,WAAa,KAC9CsC,EAAoBD,EAAMzH,OAAOoF,EAAUlM,UAAYkM,EAkB3D,OAZKmC,IACHE,GAASC,EAAoBD,EAAMzH,MAAM,GAAIoF,EAAUlM,QAAUuO,GAAS,MAAQrC,EAAY,WAI9FqC,GADED,EACO,IAIAD,GAAUG,EAAoB,GAAK,MAAQtC,EAAY,MAG3D+B,EAAW,IAAIpC,OAAO,IAAM0C,EAAOJ,EAAM7C,IAAWnO,GAe7D,SAASuO,EAAcrK,EAAMlE,EAAMmO,GAQjC,OAPKsC,EAAQzQ,KACXmO,EAAkCnO,GAAQmO,EAC1CnO,EAAO,IAGTmO,EAAUA,GAAW,GAEjBjK,aAAgBwK,OAlJtB,SAAyBxK,EAAMlE,GAE7B,IAAIsR,EAASpN,EAAKpB,OAAO2K,MAAM,aAE/B,GAAI6D,EACF,IAAK,IAAI9R,EAAI,EAAGA,EAAI8R,EAAOzO,OAAQrD,IACjCQ,EAAKuI,KAAK,CACRtH,KAAMzB,EACNoE,OAAQ,KACRmL,UAAW,KACXU,UAAU,EACVD,QAAQ,EACRD,SAAS,EACTD,UAAU,EACVI,QAAS,OAKf,OAAOoB,EAAW5M,EAAMlE,GAgIfuR,CAAerN,KAGpBuM,EAAQvM,GAxHd,SAAwBA,EAAMlE,EAAMmO,GAGlC,IAFA,IAAIqD,EAAQ,GAEHhS,EAAI,EAAGA,EAAI0E,EAAKrB,OAAQrD,IAC/BgS,EAAMjJ,KAAKgG,EAAarK,EAAK1E,GAAIQ,EAAMmO,GAASrL,QAKlD,OAAOgO,EAFM,IAAIpC,OAAO,MAAQ8C,EAAM7R,KAAK,KAAO,IAAKqR,EAAM7C,IAEnCnO,GAgHjByR,KAA0EtD,GArGrF,SAAyBjK,EAAMlE,EAAMmO,GACnC,OAAOK,EAAeH,EAAMnK,EAAMiK,GAAUnO,EAAMmO,GAuG3CuD,KAA2EvD,iEEtapF,IAAMwD,EAAQ,GACRC,EAAa,IACfC,EAAa,EAkBjB,SAASC,EAAa5N,EAAYoJ,mBAAZpJ,IAAAA,EAAO,cAAKoJ,IAAAA,EAAS,IACzB,MAATpJ,EAAeA,EAjBxB,SAAqBA,MACfyN,EAAMzN,GAAO,OAAOyN,EAAMzN,OAExB6N,EAAYxD,EAAayD,QAAQ9N,UAEnC2N,EAAaD,IACfD,EAAMzN,GAAQ6N,EACdF,KAGKE,EAOsBE,CAAY/N,EAAZ+N,CAAkB3E,EAAQ,CAAE8C,QAAQ,ICrBnE,IAAMuB,GAAQ,GACRC,GAAa,IACfC,GAAa,EAuBjB,SAASK,GAAU/O,EAAUgL,YAAAA,IAAAA,EAAU,IACd,iBAAZA,IAAwBpI,MAAMC,QAAQmI,KAC/CA,EAAU,CAAEjK,KAAMiK,UAG+CA,EAA3DjK,IAAAA,SAAM5B,MAAAA,oBAAe4O,OAAAA,oBAAgBD,UAAAA,sBAE/B,GAAG5L,OAAOnB,GAEXiO,OAAO,SAACC,EAASlO,OACvBA,GAAiB,KAATA,EAAa,OAAO,QAC7BkO,EAAS,OAAOA,QAhCxB,SAAqBlO,EAAMiK,OACnBkE,KAAclE,EAAQgD,IAAMhD,EAAQ+C,OAAS/C,EAAQ8C,UACrDqB,EAAYX,GAAMU,KAAcV,GAAMU,GAAY,OAEpDC,EAAUpO,GAAO,OAAOoO,EAAUpO,OAEhClE,EAAO,GAEP2F,EAAS,CAAE4M,OADFhE,EAAarK,EAAMlE,EAAMmO,GACfnO,KAAAA,UAErB6R,GAAaD,KACfU,EAAUpO,GAAQyB,EAClBkM,MAGKlM,EAmBoBsM,CAAY/N,EAAM,CACzCiN,IAAK7O,EACL4O,OAAAA,EACAD,UAAAA,IAHMsB,IAAAA,OAAQvS,IAAAA,KAKVyN,EAAQ8E,EAAOvD,KAAK7L,OAErBsK,EAAO,OAAO,SAEZJ,EAAkBI,KAAV+E,EAAU/E,WACnBF,EAAUpK,IAAakK,SAEzB/K,IAAUiL,EAAgB,KAEvB,CACLrJ,KAAAA,EACAmJ,IAAc,MAATnJ,GAAwB,KAARmJ,EAAa,IAAMA,EACxCE,QAAAA,EACAD,OAAQtN,EAAKmS,OAAO,SAACM,EAAM1P,EAAKQ,UAC9BkP,EAAK1P,EAAI9B,MAAQuR,EAAOjP,GACjBkP,GACN,MAEJ,UClCCC,2FACJzG,OAAA,6BAEItB,gBAAC6C,EAAcnB,cACZ,SAAA7B,GACWA,GAAV3G,UAEMhD,EAAW8K,EAAKjL,MAAMG,UAAY2J,EAAQ3J,SAO1CH,OAAa8J,GAAS3J,SAAAA,EAAU4M,MANxB9B,EAAKjL,MAAMiS,cACrBhH,EAAKjL,MAAMiS,cACXhH,EAAKjL,MAAMwD,KACXgO,GAAUrR,EAASsC,SAAUwI,EAAKjL,OAClC8J,EAAQiD,UAI0B9B,EAAKjL,MAArCwL,IAAAA,SAAU0G,IAAAA,UAAW3G,IAAAA,cAIvBlG,MAAMC,QAAQkG,IAxC5B,SAAyBA,UACmB,IAAnCvB,EAAMkI,SAASC,MAAM5G,GAuCW6G,CAAgB7G,KAC7CA,EAAW,MAIXvB,gBAAC6C,EAAcxC,UAAS5N,MAAOsD,GAC5BA,EAAM+M,MACHvB,EACsB,mBAAbA,EAGHA,EAASxL,GACXwL,EACF0G,EACAjI,EAAMhE,cAAciM,EAAWlS,GAC/BuL,EACAA,EAAOvL,GACP,KACkB,mBAAbwL,EAGLA,EAASxL,GACX,YA1CEiK,EAAMwB,WC7BX,SAAS6G,GAA8BlQ,EAAQmQ,GAC5D,GAAc,MAAVnQ,EAAgB,MAAO,GAC3B,IAEIC,EAAKvD,EAFLmD,EAAS,GACTuQ,EAAazW,OAAOuD,KAAK8C,GAG7B,IAAKtD,EAAI,EAAGA,EAAI0T,EAAWrQ,OAAQrD,IACjCuD,EAAMmQ,EAAW1T,GACY,GAAzByT,EAAS1O,QAAQxB,KACrBJ,EAAOI,GAAOD,EAAOC,IAGvB,OAAOJ,ECJT,SAASwQ,GAAgBjP,SACG,MAAnBA,EAAKd,OAAO,GAAac,EAAO,IAAMA,EAyB/C,SAASkP,GAAUvS,SACU,iBAAbA,EAAwBA,EAAWkD,EAAWlD,GAG9D,SAASwS,YACA,WACLxP,OAIJ,SAASyP,UAQHC,uJAQJC,WAAa,SAAA3S,UAAY8K,EAAK8H,WAAW5S,EAAU,WACnD6S,cAAgB,SAAA7S,UAAY8K,EAAK8H,WAAW5S,EAAU,cACtD8S,aAAe,kBAAML,MACrBM,YAAc,kBAAMN,wCAVpBG,WAAA,SAAW5S,EAAUoH,SACqBhF,KAAKvC,UAArCmT,SAAAA,aAAW,SAAIrJ,QAAAA,aAAU,KACjCA,EAAQvC,OAASA,EACjBuC,EAAQ3J,SA5CZ,SAAqBgT,EAAUhT,UACxBgT,OAGAhT,GACHsC,SAAUgQ,GAAgBU,GAAYhT,EAASsC,WAJ3BtC,EA2CDiT,CAAYD,EAAU1P,EAAetD,IACxD2J,EAAQ6C,IAAM+F,GAAU5I,EAAQ3J,aAQlCoL,OAAA,iBACmEhJ,KAAKvC,UAA9DmT,SAAAA,aAAW,SAAIrJ,QAAAA,aAAU,SAAI3J,SAAAA,aAAW,MAAQkT,0CAElD/K,EAAU,CACdM,WAAY,SAAApF,UAAQiP,GAAgBU,EAAWT,GAAUlP,KACzD+D,OAAQ,MACRpH,SAlDN,SAAuBgT,EAAUhT,OAC1BgT,EAAU,OAAOhT,MAEhBmT,EAAOb,GAAgBU,UAEW,IAApChT,EAASsC,SAASoB,QAAQyP,GAAoBnT,OAG7CA,GACHsC,SAAUtC,EAASsC,SAASqB,OAAOwP,EAAKnR,UAyC5BoR,CAAcJ,EAAU1P,EAAetD,IACjD0H,KAAMtF,KAAKuQ,WACX3J,QAAS5G,KAAKyQ,cACdnK,GAAI8J,KACJvJ,OAAQuJ,KACRtJ,UAAWsJ,KACXnJ,OAAQjH,KAAK0Q,aACb1J,MAAOhH,KAAK2Q,oBAGPjJ,gBAACoC,OAAWgH,GAAM/K,QAASA,EAASkE,cAAe1C,SA7BnCG,EAAMwB,WCzC3B+H,2FACJjI,OAAA,6BAEItB,gBAAC6C,EAAcnB,cACZ,SAAA7B,GACWA,GAAV3G,UAII/B,EAAS2L,EAFP5M,EAAW8K,EAAKjL,MAAMG,UAAY2J,EAAQ3J,gBAQhD8J,EAAMkI,SAAS/S,QAAQ6L,EAAKjL,MAAMwL,SAAU,SAAAiI,MAC7B,MAAT1G,GAAiB9C,EAAMyJ,eAAeD,GAAQ,KAG1CjQ,GAFNpC,EAAUqS,GAESzT,MAAMwD,MAAQiQ,EAAMzT,MAAMqE,KAE7C0I,EAAQvJ,EACJgO,GAAUrR,EAASsC,cAAegR,EAAMzT,OAAOwD,KAAAA,KAC/CsG,EAAQiD,SAITA,EACH9C,EAAM0J,aAAavS,EAAS,CAAEjB,SAAAA,EAAU8R,cAAelF,IACvD,WA7BO9C,EAAMwB,WCJvBmI,GAAgB,CAChBlI,mBAAmB,EACnBmI,aAAa,EACb1H,cAAc,EACd2H,cAAc,EACd9J,aAAa,EACb+J,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,QAAQ,EACRC,WAAW,EACXlW,MAAM,GAGNmW,GAAgB,CAChB7T,MAAM,EACN4B,QAAQ,EACR9F,WAAW,EACXgY,QAAQ,EACRC,QAAQ,EACRpS,WAAW,EACXqS,OAAO,GAWPC,GAAe,CACfxW,UAAY,EACZyW,SAAS,EACTX,cAAc,EACd9J,aAAa,EACbmK,WAAW,EACXlW,MAAM,GAGNyW,GAAe,GAGnB,SAASC,GAAWzC,GAChB,OAAI0C,EAAQC,OAAO3C,GACRsC,GAEJE,GAAaxC,EAAoB,WAAM0B,GANlDc,GAAaE,EAAQE,YAlBK,CACtB9W,UAAY,EACZuN,QAAQ,EACRuI,cAAc,EACd9J,aAAa,EACbmK,WAAW,GAsBf,IAAI3X,GAAiBT,OAAOS,eACxBoC,GAAsB7C,OAAO6C,oBAC7BP,GAAwBtC,OAAOsC,sBAC/B0W,GAA2BhZ,OAAOgZ,yBAClCC,GAAiBjZ,OAAOiZ,eACxBC,GAAkBlZ,OAAOM,UAuC7B,OArCA,SAAS6Y,EAAqBC,EAAiBC,EAAiBC,GAC5D,GAA+B,iBAApBD,EAiCX,OAAOD,EA9BH,GAAIF,GAAiB,CACjB,IAAIK,EAAqBN,GAAeI,GACpCE,GAAsBA,IAAuBL,IAC7CC,EAAqBC,EAAiBG,EAAoBD,GAIlE,IAAI/V,EAAOV,GAAoBwW,GAE3B/W,KACAiB,EAAOA,EAAKqF,OAAOtG,GAAsB+W,KAM7C,IAHA,IAAIG,EAAgBZ,GAAWQ,GAC3BK,EAAgBb,GAAWS,GAEtBtW,EAAI,EAAGA,EAAIQ,EAAK6C,SAAUrD,EAAG,CAClC,IAAIuD,EAAM/C,EAAKR,GACf,KAAKsV,GAAc/R,IAAUgT,GAAaA,EAAUhT,IAAWmT,GAAiBA,EAAcnT,IAAWkT,GAAiBA,EAAclT,IAAO,CAC3I,IAAIoT,EAAaV,GAAyBK,EAAiB/S,GAC3D,IAEI7F,GAAe2Y,EAAiB9S,EAAKoT,GACvC,MAAOzY,MAIjB,OAAOmY,GCzFf,IAAMO,GAAazL,EAAMyL,WAalB,SAASC,YAQPD,GAAW5I,GAAe3M,mCClBnC,gBAAkByV,IAAAA,YAASC,KAAAA,uBAEvB5L,gBAAC6C,EAAcnB,cACZ,SAAA7B,MACWA,GAAV3G,OAEK0S,GAAQ/L,EAAQ0C,cAAe,OAAO,SAErCsJ,EAAShM,EAAQxB,QAAQiB,aAG7BU,gBAACkD,GACCC,QAAS,SAAA2I,GACPA,EAAKC,QAAUF,EAAOF,IAExB7J,SAAU,SAACgK,EAAMzI,GACXA,EAAUsI,UAAYA,IACxBG,EAAKC,UACLD,EAAKC,QAAUF,EAAOF,KAG1BrI,UAAW,SAAAwI,GACTA,EAAKC,WAEPJ,QAASA,kBCtBrB,gBAAoB3D,IAAAA,cAAe7N,IAAAA,OAAIyD,KAAAA,uBAEnCoC,gBAAC6C,EAAcnB,cACZ,SAAA7B,GACWA,GAAV3G,UAEQmF,EAA2BwB,EAA3BxB,QAASkE,EAAkB1C,EAAlB0C,cAEXsJ,EAASjO,EAAOS,EAAQT,KAAOS,EAAQa,QACvChJ,EAAWsD,EACfwO,EACkB,iBAAP7N,EACLgN,EAAahN,EAAI6N,EAAcrF,aAE1BxI,GACH3B,SAAU2O,EAAahN,EAAG3B,SAAUwP,EAAcrF,UAEtDxI,UAKFoI,GACFsJ,EAAO3V,GACA,MAIP8J,gBAACkD,GACCC,QAAS,WACP0I,EAAO3V,IAET4L,SAAU,SAACgK,EAAMzI,OACT2I,EAAexS,EAAe6J,EAAUlJ,IAE3Ce,EAAkB8Q,OACd9V,GACHkC,IAAK4T,EAAa5T,QAGpByT,EAAO3V,IAGXiE,GAAIA,iJF9CT,kBAQEsR,GAAW1I,iCAcb,eAQCD,EAAQ2I,GAAW5I,GAAeC,aACjCA,EAAQA,EAAMH,OAAS,oBAGzB,SAAuBpJ,OAQtBrD,EAAWwV,KACX5I,EAAQ2I,GAAW5I,GAAeC,aACjCvJ,EAAOgO,GAAUrR,EAASsC,SAAUe,GAAQuJ,gBG3CrD,SAAoBtB,GAER,SAAJyK,EAAIlW,OACAmW,EAA2CnW,EAA3CmW,oBAAwBC,KAAmBpW,kCAGjDiK,gBAAC6C,EAAcnB,cACZ,SAAA7B,UAEGA,GADF3G,MAKE8G,gBAACwB,OACK2K,EACAtM,GACJuM,IAAKF,WAfXnM,iBAA4ByB,EAAUzB,aAAeyB,EAAUlL,iBAuBrE2V,EAAElM,YAAcA,EAChBkM,EAAEI,iBAAmB7K,EAYd8K,GAAaL,EAAGzK"}